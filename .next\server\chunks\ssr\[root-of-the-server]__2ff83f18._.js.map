{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport { useRouter } from 'next/navigation'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport {\n  HomeIcon,\n  CheckSquareIcon,\n  DollarSignIcon,\n  ShoppingCartIcon,\n  MessageCircleIcon,\n  ChefHatIcon,\n  LogOutIcon,\n  MenuIcon,\n  XIcon,\n  UserIcon,\n  SettingsIcon,\n  BellIcon,\n  SparklesIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n  TrendingUpIcon,\n  TargetIcon,\n  CalendarIcon,\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: HomeIcon,\n    color: 'text-blue-600',\n    badge: null,\n    description: 'Overview & Analytics'\n  },\n  {\n    name: 'Tasks',\n    href: '/tasks',\n    icon: CheckSquareIcon,\n    color: 'text-green-600',\n    badge: quickStats.tasksToday,\n    description: 'Manage your tasks'\n  },\n  {\n    name: 'Budget',\n    href: '/budget',\n    icon: DollarSignIcon,\n    color: 'text-emerald-600',\n    badge: quickStats.budgetAlert ? '!' : null,\n    description: 'Track expenses'\n  },\n  {\n    name: 'Shopping Lists',\n    href: '/shopping',\n    icon: ShoppingCartIcon,\n    color: 'text-purple-600',\n    badge: null,\n    description: 'Shopping & Lists'\n  },\n  {\n    name: 'AI Chat',\n    href: '/chat',\n    icon: MessageCircleIcon,\n    color: 'text-indigo-600',\n    badge: null,\n    description: 'AI Assistant'\n  },\n  {\n    name: 'Recipes',\n    href: '/recipes',\n    icon: ChefHatIcon,\n    color: 'text-orange-600',\n    badge: null,\n    description: 'Recipe Collection'\n  },\n]\n\nexport default function Sidebar() {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const [isCollapsed, setIsCollapsed] = useState(false)\n  const [user, setUser] = useState<any>(null)\n  const [notifications, setNotifications] = useState(3)\n  const [quickStats, setQuickStats] = useState({\n    tasksToday: 5,\n    budgetAlert: true,\n    upcomingDeadlines: 2\n  })\n  const pathname = usePathname()\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n    }\n    getUser()\n\n    // Load sidebar state from localStorage\n    const savedCollapsed = localStorage.getItem('sidebar-collapsed')\n    if (savedCollapsed) {\n      setIsCollapsed(JSON.parse(savedCollapsed))\n    }\n  }, [])\n\n  const toggleCollapsed = () => {\n    const newState = !isCollapsed\n    setIsCollapsed(newState)\n    localStorage.setItem('sidebar-collapsed', JSON.stringify(newState))\n  }\n\n  const handleLogout = async () => {\n    await supabase.auth.signOut()\n    router.push('/login')\n    router.refresh()\n  }\n\n  const getUserInitials = (email: string) => {\n    return email.split('@')[0].slice(0, 2).toUpperCase()\n  }\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white hover:bg-gray-600 transition-colors\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XIcon className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n\n          {/* Header with logo and user */}\n          <div className=\"flex-shrink-0 px-4 py-6 bg-gradient-to-r from-blue-600 to-indigo-600\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  <SparklesIcon className=\"h-8 w-8 text-white\" />\n                </div>\n                <h1 className=\"text-xl font-bold text-white\">LifeManager</h1>\n              </div>\n            </div>\n            {user && (\n              <div className=\"mt-4 flex items-center space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-white\">\n                      {getUserInitials(user.email)}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"min-w-0 flex-1\">\n                  <p className=\"text-sm font-medium text-white truncate\">\n                    {user.email}\n                  </p>\n                  <p className=\"text-xs text-blue-100\">\n                    Welcome back!\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <nav className=\"px-3 space-y-1\" role=\"navigation\" aria-label=\"Main navigation\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setSidebarOpen(false)}\n                    className={`group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${\n                      isActive\n                        ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-600'\n                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon\n                      className={`mr-4 h-6 w-6 transition-colors ${\n                        isActive ? 'text-blue-600' : `${item.color} group-hover:${item.color}`\n                      }`}\n                      aria-hidden=\"true\"\n                    />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 border-t border-gray-200 p-4\">\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <LogOutIcon className=\"mr-3 h-5 w-5\" />\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <motion.div\n        className=\"hidden md:flex md:flex-col md:fixed md:inset-y-0 z-30\"\n        animate={{ width: isCollapsed ? 80 : 280 }}\n        transition={{ duration: 0.3, ease: \"easeInOut\" }}\n      >\n        <div className=\"flex-1 flex flex-col min-h-0 bg-white shadow-xl border-r border-gray-200\">\n          {/* Header with logo and user */}\n          <div className=\"flex-shrink-0 px-4 py-6 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 relative overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm\"></div>\n            <div className=\"relative\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <motion.div\n                    animate={{ scale: isCollapsed ? 1.2 : 1 }}\n                    transition={{ duration: 0.3 }}\n                    className=\"flex-shrink-0\"\n                  >\n                    <SparklesIcon className=\"h-8 w-8 text-white\" />\n                  </motion.div>\n                  <AnimatePresence>\n                    {!isCollapsed && (\n                      <motion.h1\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        exit={{ opacity: 0, x: -20 }}\n                        transition={{ duration: 0.2 }}\n                        className=\"text-xl font-bold text-white\"\n                      >\n                        LifeManager\n                      </motion.h1>\n                    )}\n                  </AnimatePresence>\n                </div>\n\n                <motion.button\n                  onClick={toggleCollapsed}\n                  className=\"p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {isCollapsed ? (\n                    <ChevronRightIcon className=\"h-4 w-4 text-white\" />\n                  ) : (\n                    <ChevronLeftIcon className=\"h-4 w-4 text-white\" />\n                  )}\n                </motion.button>\n              </div>\n\n              <AnimatePresence>\n                {user && !isCollapsed && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -20 }}\n                    transition={{ duration: 0.3, delay: 0.1 }}\n                    className=\"mt-4 flex items-center space-x-3\"\n                  >\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center ring-2 ring-white/30\">\n                        <span className=\"text-sm font-medium text-white\">\n                          {getUserInitials(user.email)}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"min-w-0 flex-1\">\n                      <p className=\"text-sm font-medium text-white truncate\">\n                        {user.email}\n                      </p>\n                      <p className=\"text-xs text-blue-100\">\n                        Welcome back! 👋\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n          </div>\n\n          {/* Quick Stats */}\n          <AnimatePresence>\n            {!isCollapsed && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                transition={{ duration: 0.3 }}\n                className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\"\n              >\n                <div className=\"grid grid-cols-3 gap-2 text-center\">\n                  <div className=\"p-2 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-lg font-bold text-blue-600\">{quickStats.tasksToday}</div>\n                    <div className=\"text-xs text-gray-600\">Today</div>\n                  </div>\n                  <div className=\"p-2 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-lg font-bold text-green-600\">85%</div>\n                    <div className=\"text-xs text-gray-600\">Complete</div>\n                  </div>\n                  <div className=\"p-2 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-lg font-bold text-orange-600\">{quickStats.upcomingDeadlines}</div>\n                    <div className=\"text-xs text-gray-600\">Due</div>\n                  </div>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <nav className=\"flex-1 px-3 space-y-2\" role=\"navigation\" aria-label=\"Main navigation\">\n              {navigation.map((item, index) => {\n                const isActive = pathname === item.href\n                return (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      className={`group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 relative overflow-hidden ${\n                        isActive\n                          ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-md border border-blue-200'\n                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'\n                      }`}\n                      aria-current={isActive ? 'page' : undefined}\n                      title={isCollapsed ? item.name : undefined}\n                    >\n                      {isActive && (\n                        <motion.div\n                          layoutId=\"activeTab\"\n                          className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-indigo-500/10\"\n                          transition={{ type: \"spring\", bounce: 0.2, duration: 0.6 }}\n                        />\n                      )}\n\n                      <div className=\"relative flex items-center w-full\">\n                        <motion.div\n                          whileHover={{ scale: 1.1 }}\n                          whileTap={{ scale: 0.95 }}\n                          className=\"flex-shrink-0\"\n                        >\n                          <item.icon\n                            className={`h-5 w-5 transition-colors ${\n                              isActive ? 'text-blue-600' : `${item.color} group-hover:${item.color}`\n                            }`}\n                            aria-hidden=\"true\"\n                          />\n                        </motion.div>\n\n                        <AnimatePresence>\n                          {!isCollapsed && (\n                            <motion.div\n                              initial={{ opacity: 0, x: -10 }}\n                              animate={{ opacity: 1, x: 0 }}\n                              exit={{ opacity: 0, x: -10 }}\n                              transition={{ duration: 0.2 }}\n                              className=\"ml-3 flex-1 flex items-center justify-between\"\n                            >\n                              <div>\n                                <div className=\"font-medium\">{item.name}</div>\n                                <div className=\"text-xs text-gray-500\">{item.description}</div>\n                              </div>\n\n                              {item.badge && (\n                                <motion.div\n                                  initial={{ scale: 0 }}\n                                  animate={{ scale: 1 }}\n                                  className={`ml-2 px-2 py-1 text-xs font-bold rounded-full ${\n                                    item.badge === '!'\n                                      ? 'bg-red-100 text-red-600'\n                                      : 'bg-blue-100 text-blue-600'\n                                  }`}\n                                >\n                                  {item.badge}\n                                </motion.div>\n                              )}\n                            </motion.div>\n                          )}\n                        </AnimatePresence>\n\n                        {isCollapsed && item.badge && (\n                          <motion.div\n                            initial={{ scale: 0 }}\n                            animate={{ scale: 1 }}\n                            className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                          />\n                        )}\n                      </div>\n                    </Link>\n                  </motion.div>\n                )\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 border-t border-gray-200 p-4\">\n            <motion.button\n              onClick={handleLogout}\n              className={`w-full flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors ${\n                isCollapsed ? 'justify-center' : ''\n              }`}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              title={isCollapsed ? 'Logout' : undefined}\n            >\n              <LogOutIcon className=\"h-5 w-5\" />\n              <AnimatePresence>\n                {!isCollapsed && (\n                  <motion.span\n                    initial={{ opacity: 0, x: -10 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: -10 }}\n                    transition={{ duration: 0.2 }}\n                    className=\"ml-3\"\n                  >\n                    Logout\n                  </motion.span>\n                )}\n              </AnimatePresence>\n            </motion.button>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Mobile menu button */}\n      <div className=\"md:hidden\">\n        <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-4 py-3 shadow-sm sticky top-0 z-40\">\n          <div className=\"flex items-center space-x-2\">\n            <SparklesIcon className=\"h-6 w-6 text-blue-600\" />\n            <h1 className=\"text-lg font-semibold text-gray-900 truncate\">LifeManager</h1>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            {user && (\n              <div className=\"flex items-center space-x-2 mr-2\">\n                <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n                  <span className=\"text-xs font-medium text-blue-700\">\n                    {getUserInitials(user.email)}\n                  </span>\n                </div>\n              </div>\n            )}\n            <button\n              type=\"button\"\n              className=\"touch-target inline-flex items-center justify-center p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors\"\n            >\n              <BellIcon className=\"h-5 w-5\" />\n            </button>\n            <button\n              type=\"button\"\n              className=\"touch-target inline-flex items-center justify-center p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <MenuIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AA6BA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,WAAQ;QACd,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;QACrB,OAAO;QACP,OAAO,WAAW,UAAU;QAC5B,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sNAAA,CAAA,iBAAc;QACpB,OAAO;QACP,OAAO,WAAW,WAAW,GAAG,MAAM;QACtC,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0NAAA,CAAA,mBAAgB;QACtB,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,4NAAA,CAAA,oBAAiB;QACvB,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gNAAA,CAAA,cAAW;QACjB,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,YAAY;QACZ,aAAa;QACb,mBAAmB;IACrB;IACA,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,QAAQ;QACV;QACA;QAEA,uCAAuC;QACvC,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI,gBAAgB;YAClB,eAAe,KAAK,KAAK,CAAC;QAC5B;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,MAAM,WAAW,CAAC;QAClB,eAAe;QACf,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,MAAM,eAAe;QACnB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,OAAO,IAAI,CAAC;QACZ,OAAO,OAAO;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;IACpD;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA6D,SAAS,IAAM,eAAe;;;;;;kCAC1G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC,gMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC;oDAAG,WAAU;8DAA+B;;;;;;;;;;;;;;;;;oCAGhD,sBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAiB,MAAK;oCAAa,cAAW;8CAC1D,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI;wCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe;4CAC9B,WAAW,CAAC,uKAAuK,EACjL,WACI,wDACA,sDACJ;4CACF,gBAAc,WAAW,SAAS;;8DAElC,8OAAC,KAAK,IAAI;oDACR,WAAW,CAAC,+BAA+B,EACzC,WAAW,kBAAkB,GAAG,KAAK,KAAK,CAAC,aAAa,EAAE,KAAK,KAAK,EAAE,EACtE;oDACF,eAAY;;;;;;gDAEb,KAAK,IAAI;;2CAhBL,KAAK,IAAI;;;;;oCAmBpB;;;;;;;;;;;0CAIJ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO,cAAc,KAAK;gBAAI;gBACzC,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAY;0BAE/C,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,OAAO,cAAc,MAAM;4DAAE;4DACxC,YAAY;gEAAE,UAAU;4DAAI;4DAC5B,WAAU;sEAEV,cAAA,8OAAC,8MAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,8OAAC,yLAAA,CAAA,kBAAe;sEACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gEACR,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,MAAM;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC3B,YAAY;oEAAE,UAAU;gEAAI;gEAC5B,WAAU;0EACX;;;;;;;;;;;;;;;;;8DAOP,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DAEvB,4BACC,8OAAC,0NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;6EAE5B,8OAAC,wNAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKjC,8OAAC,yLAAA,CAAA,kBAAe;sDACb,QAAQ,CAAC,6BACR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;kEAIjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EACV,KAAK,KAAK;;;;;;0EAEb,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWjD,8OAAC,yLAAA,CAAA,kBAAe;sCACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC,YAAW,UAAU;;;;;;8DACvE,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC,YAAW,iBAAiB;;;;;;8DAChF,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAa,cAAW;0CACjE,WAAW,GAAG,CAAC,CAAC,MAAM;oCACrB,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAK;kDAEjD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,8LAA8L,EACxM,WACI,8FACA,sEACJ;4CACF,gBAAc,WAAW,SAAS;4CAClC,OAAO,cAAc,KAAK,IAAI,GAAG;;gDAEhC,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,UAAS;oDACT,WAAU;oDACV,YAAY;wDAAE,MAAM;wDAAU,QAAQ;wDAAK,UAAU;oDAAI;;;;;;8DAI7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAK;4DACxB,WAAU;sEAEV,cAAA,8OAAC,KAAK,IAAI;gEACR,WAAW,CAAC,0BAA0B,EACpC,WAAW,kBAAkB,GAAG,KAAK,KAAK,CAAC,aAAa,EAAE,KAAK,KAAK,EAAE,EACtE;gEACF,eAAY;;;;;;;;;;;sEAIhB,8OAAC,yLAAA,CAAA,kBAAe;sEACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,MAAM;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC3B,YAAY;oEAAE,UAAU;gEAAI;gEAC5B,WAAU;;kFAEV,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAe,KAAK,IAAI;;;;;;0FACvC,8OAAC;gFAAI,WAAU;0FAAyB,KAAK,WAAW;;;;;;;;;;;;oEAGzD,KAAK,KAAK,kBACT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,SAAS;4EAAE,OAAO;wEAAE;wEACpB,SAAS;4EAAE,OAAO;wEAAE;wEACpB,WAAW,CAAC,8CAA8C,EACxD,KAAK,KAAK,KAAK,MACX,4BACA,6BACJ;kFAED,KAAK,KAAK;;;;;;;;;;;;;;;;;wDAOpB,eAAe,KAAK,KAAK,kBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,OAAO;4DAAE;4DACpB,SAAS;gEAAE,OAAO;4DAAE;4DACpB,WAAU;;;;;;;;;;;;;;;;;;uCAxEb,KAAK,IAAI;;;;;gCA+EpB;;;;;;;;;;;sCAIJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,WAAW,CAAC,mHAAmH,EAC7H,cAAc,mBAAmB,IACjC;gCACF,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,OAAO,cAAc,WAAW;;kDAEhC,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC,yLAAA,CAAA,kBAAe;kDACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;;;;;;;sCAE/D,8OAAC;4BAAI,WAAU;;gCACZ,sBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;8CAKnC,8OAAC;oCACC,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/AuthenticatedLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport Sidebar from './Sidebar'\n\ninterface AuthenticatedLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {\n  const [loading, setLoading] = useState(true)\n  const [user, setUser] = useState<any>(null)\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const { data: { user }, error } = await supabase.auth.getUser()\n        \n        if (error || !user) {\n          router.push('/login')\n          return\n        }\n        \n        setUser(user)\n      } catch (error) {\n        console.error('Auth check error:', error)\n        router.push('/login')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    checkAuth()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_OUT' || !session) {\n          router.push('/login')\n        } else if (event === 'SIGNED_IN' && session) {\n          setUser(session.user)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [router, supabase.auth])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-6 animate-fade-in\">\n          <div className=\"relative\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-blue-200\"></div>\n            <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent absolute top-0 left-0\"></div>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-gray-700 text-lg font-medium\">Loading your workspace...</p>\n            <p className=\"text-gray-500 text-sm mt-1\">Please wait while we prepare everything</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect to login\n  }\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50\">\n      {/* Skip Navigation Links */}\n      <a\n        href=\"#main-content\"\n        className=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n      >\n        Skip to main content\n      </a>\n\n      <Sidebar />\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        <main\n          id=\"main-content\"\n          className=\"flex-1 relative overflow-y-auto focus:outline-none transition-all duration-300 md:ml-20 lg:ml-72\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"py-6 animate-fade-in\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWe,SAAS,oBAAoB,EAAE,QAAQ,EAA4B;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;gBAE7D,IAAI,SAAS,CAAC,MAAM;oBAClB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,IAAI,UAAU,gBAAgB,CAAC,SAAS;gBACtC,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,eAAe,SAAS;gBAC3C,QAAQ,QAAQ,IAAI;YACtB;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAQ,SAAS,IAAI;KAAC;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,yBAAyB;;IACvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;0BAID,8OAAC,uIAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,IAAG;oBACH,WAAU;oBACV,MAAK;oBACL,cAAW;8BAEX,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}