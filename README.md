# LifeManager - Complete Life Management SaaS

A comprehensive life management platform built with Next.js, TypeScript, Tailwind CSS, and Supabase. Manage your tasks, budget, shopping lists, recipes, and get AI assistance all in one place.

## 🚀 Features

### ✅ **Task Management**
- Create, edit, and delete tasks
- Set priorities (low, medium, high)
- Add due dates and categories
- Mark tasks as complete
- Filter and organize tasks

### 💰 **Budget Management**
- Track income and expenses
- Create custom budget categories
- View monthly financial summaries
- Transaction history with detailed analytics
- Visual spending insights

### 🛒 **Shopping Lists**
- Create multiple shopping lists
- Add items with quantities and units
- Mark items as completed
- Add notes for specific items
- Organize by categories

### 🤖 **AI Chat Assistant**
- Get personalized life management advice
- Ask questions about tasks, budgets, and planning
- Conversational interface with chat history
- Context-aware responses

### 🍳 **Recipe Management**
- Save and organize recipes
- Auto-import recipes from URLs
- Ingredient lists and step-by-step instructions
- Cooking times and difficulty levels
- Recipe search and categorization

### 🔐 **Authentication & Security**
- Secure user authentication with Supabase
- Google OAuth integration
- Row-level security (RLS) for data protection
- Session management

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Lucide React Icons
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Testing**: Jest, React Testing Library
- **Development**: ESLint, Turbopack

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd LifeManager
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Fill in your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   OPENAI_API_KEY=your_openai_api_key (optional)
   ```

4. **Set up the database**
   - Create a new Supabase project
   - Run the migration files in `supabase/migrations/` in order:
     - `001_initial_schema.sql`
     - `002_rls_policies.sql`
     - `003_functions_and_triggers.sql`

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

Generate coverage report:
```bash
npm run test:coverage
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── (auth)/            # Authentication pages
│   ├── dashboard/         # Main dashboard
│   ├── tasks/             # Task management
│   ├── budget/            # Budget tracking
│   ├── shopping/          # Shopping lists
│   ├── chat/              # AI chat interface
│   ├── recipes/           # Recipe management
│   └── api/               # API routes
├── components/            # Reusable components
│   ├── ui/                # UI components
│   └── layout/            # Layout components
├── lib/                   # Utility functions
│   ├── supabase/          # Supabase client configuration
│   ├── types/             # TypeScript type definitions
│   └── utils.ts           # Helper functions
└── __tests__/             # Test files
```

## 🗄️ Database Schema

The application uses PostgreSQL with the following main tables:
- `profiles` - User profiles
- `tasks` - Task management
- `budget_categories` - Budget categories
- `transactions` - Financial transactions
- `shopping_lists` - Shopping lists
- `shopping_list_items` - Shopping list items
- `recipes` - Recipe storage
- `chat_conversations` - Chat conversations
- `chat_messages` - Chat messages

## 🔧 Configuration

### Supabase Setup
1. Create a new Supabase project
2. Enable Google OAuth in Authentication settings
3. Run the provided SQL migrations
4. Configure RLS policies for security

### Recipe Import
The recipe import feature uses web scraping to extract recipe data from URLs. It supports:
- JSON-LD structured data
- Common HTML patterns
- Automatic ingredient and instruction parsing

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms
The application can be deployed on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, please open an issue on GitHub or contact the development team.

---

Built with ❤️ using Next.js and Supabase
