import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import ToastProvider from "@/components/providers/ToastProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "LifeManager - Your Personal Life Management Platform",
  description: "Manage your tasks, budget, shopping lists, recipes, and more with AI assistance. The complete life management solution.",
  keywords: "life management, task management, budget tracking, shopping lists, recipes, AI assistant",
  authors: [{ name: "LifeManager Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased h-full bg-gray-50`}
      >
        <div className="min-h-full">
          {children}
          <ToastProvider />
        </div>
      </body>
    </html>
  );
}
