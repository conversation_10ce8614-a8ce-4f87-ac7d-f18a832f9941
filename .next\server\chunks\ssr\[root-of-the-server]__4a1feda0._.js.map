{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/tasks/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { PlusIcon, CheckIcon, XIcon, CalendarIcon, FlagIcon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\n\ntype Task = Database['public']['Tables']['tasks']['Row']\n\nexport default function TasksPage() {\n  const [tasks, setTasks] = useState<Task[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [newTask, setNewTask] = useState({\n    title: '',\n    description: '',\n    priority: 'medium' as 'low' | 'medium' | 'high',\n    due_date: '',\n    category: '',\n  })\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchTasks()\n  }, [])\n\n  const fetchTasks = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('tasks')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setTasks(data || [])\n    } catch (error) {\n      console.error('Error fetching tasks:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addTask = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('tasks').insert([\n        {\n          ...newTask,\n          user_id: user.id,\n          due_date: newTask.due_date || null,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewTask({\n        title: '',\n        description: '',\n        priority: 'medium',\n        due_date: '',\n        category: '',\n      })\n      setShowAddForm(false)\n      fetchTasks()\n    } catch (error) {\n      console.error('Error adding task:', error)\n    }\n  }\n\n  const toggleTask = async (taskId: string, completed: boolean) => {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .update({ completed: !completed })\n        .eq('id', taskId)\n\n      if (error) throw error\n      fetchTasks()\n    } catch (error) {\n      console.error('Error updating task:', error)\n    }\n  }\n\n  const deleteTask = async (taskId: string) => {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .delete()\n        .eq('id', taskId)\n\n      if (error) throw error\n      fetchTasks()\n    } catch (error) {\n      console.error('Error deleting task:', error)\n    }\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-600 bg-red-100'\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100'\n      case 'low':\n        return 'text-green-600 bg-green-100'\n      default:\n        return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div>\n      <div className=\"mb-8 flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Tasks</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Manage your tasks and stay organized\n          </p>\n        </div>\n        <button\n          onClick={() => setShowAddForm(true)}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n        >\n          <PlusIcon className=\"h-4 w-4 mr-2\" />\n          Add Task\n        </button>\n      </div>\n\n      {/* Add Task Form */}\n      {showAddForm && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <form onSubmit={addTask}>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700\">\n                  Title\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.title}\n                  onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}\n                />\n              </div>\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n                  Description\n                </label>\n                <textarea\n                  id=\"description\"\n                  rows={3}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.description}\n                  onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"priority\" className=\"block text-sm font-medium text-gray-700\">\n                  Priority\n                </label>\n                <select\n                  id=\"priority\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.priority}\n                  onChange={(e) => setNewTask({ ...newTask, priority: e.target.value as 'low' | 'medium' | 'high' })}\n                >\n                  <option value=\"low\">Low</option>\n                  <option value=\"medium\">Medium</option>\n                  <option value=\"high\">High</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"due_date\" className=\"block text-sm font-medium text-gray-700\">\n                  Due Date\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  id=\"due_date\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.due_date}\n                  onChange={(e) => setNewTask({ ...newTask, due_date: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700\">\n                  Category\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"category\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.category}\n                  onChange={(e) => setNewTask({ ...newTask, category: e.target.value })}\n                  placeholder=\"e.g., Work, Personal, Health\"\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddForm(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Add Task\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Tasks List */}\n      <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n        {tasks.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <CheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No tasks</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">Get started by creating a new task.</p>\n          </div>\n        ) : (\n          <ul className=\"divide-y divide-gray-200\">\n            {tasks.map((task) => (\n              <li key={task.id} className=\"px-6 py-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <button\n                      onClick={() => toggleTask(task.id, task.completed)}\n                      className={`flex-shrink-0 h-5 w-5 rounded border-2 flex items-center justify-center ${\n                        task.completed\n                          ? 'bg-blue-600 border-blue-600'\n                          : 'border-gray-300 hover:border-blue-500'\n                      }`}\n                    >\n                      {task.completed && <CheckIcon className=\"h-3 w-3 text-white\" />}\n                    </button>\n                    <div className=\"ml-3\">\n                      <p className={`text-sm font-medium ${\n                        task.completed ? 'line-through text-gray-500' : 'text-gray-900'\n                      }`}>\n                        {task.title}\n                      </p>\n                      {task.description && (\n                        <p className=\"text-sm text-gray-500\">{task.description}</p>\n                      )}\n                      <div className=\"flex items-center mt-1 space-x-4\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>\n                          <FlagIcon className=\"h-3 w-3 mr-1\" />\n                          {task.priority}\n                        </span>\n                        {task.category && (\n                          <span className=\"text-xs text-gray-500\">{task.category}</span>\n                        )}\n                        {task.due_date && (\n                          <span className=\"inline-flex items-center text-xs text-gray-500\">\n                            <CalendarIcon className=\"h-3 w-3 mr-1\" />\n                            {new Date(task.due_date).toLocaleDateString()}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => deleteTask(task.id)}\n                    className=\"text-red-600 hover:text-red-900\"\n                  >\n                    <XIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </li>\n            ))}\n          </ul>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AASe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IACA,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,SAAS,QAAQ,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU,OAAO;QACrB,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;gBACpD;oBACE,GAAG,OAAO;oBACV,SAAS,KAAK,EAAE;oBAChB,UAAU,QAAQ,QAAQ,IAAI;gBAChC;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,WAAW;gBACT,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,UAAU;YACZ;YACA,eAAe;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,aAAa,OAAO,QAAgB;QACxC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;gBAAE,WAAW,CAAC;YAAU,GAC/B,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,8OAAC,sMAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMxC,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU;;sCACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,QAAQ;4CACR,WAAU;4CACV,OAAO,QAAQ,KAAK;4CACpB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAA0C;;;;;;sDAGjF,8OAAC;4CACC,IAAG;4CACH,MAAM;4CACN,WAAU;4CACV,OAAO,QAAQ,WAAW;4CAC1B,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAG1E,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAA8B;;8DAEhG,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAGzB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,WAAU;4CACV,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAGvE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,WAAU;4CACV,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACnE,aAAY;;;;;;;;;;;;;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wMAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;yCAG5C,8OAAC;oBAAG,WAAU;8BACX,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4BAAiB,WAAU;sCAC1B,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,WAAW,KAAK,EAAE,EAAE,KAAK,SAAS;gDACjD,WAAW,CAAC,wEAAwE,EAClF,KAAK,SAAS,GACV,gCACA,yCACJ;0DAED,KAAK,SAAS,kBAAI,8OAAC,wMAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAE1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAW,CAAC,oBAAoB,EACjC,KAAK,SAAS,GAAG,+BAA+B,iBAChD;kEACC,KAAK,KAAK;;;;;;oDAEZ,KAAK,WAAW,kBACf,8OAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;kEAExD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,iBAAiB,KAAK,QAAQ,GAAG;;kFAC3H,8OAAC,sMAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,KAAK,QAAQ;;;;;;;4DAEf,KAAK,QAAQ,kBACZ,8OAAC;gEAAK,WAAU;0EAAyB,KAAK,QAAQ;;;;;;4DAEvD,KAAK,QAAQ,kBACZ,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,8MAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEACvB,IAAI,KAAK,KAAK,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;kDAMrD,8OAAC;wCACC,SAAS,IAAM,WAAW,KAAK,EAAE;wCACjC,WAAU;kDAEV,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BA3Cd,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAqD9B", "debugId": null}}]}