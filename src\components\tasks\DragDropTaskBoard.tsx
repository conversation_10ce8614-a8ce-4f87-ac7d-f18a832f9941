'use client'

import { useState } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { 
  CalendarIcon, 
  FlagIcon, 
  GripVerticalIcon,
  CheckIcon,
  XIcon,
  ClockIcon
} from 'lucide-react'
import { Database } from '@/lib/types/database'
import { motion, AnimatePresence } from 'framer-motion'

type Task = Database['public']['Tables']['tasks']['Row']

interface TaskCardProps {
  task: Task
  onToggle: (taskId: string, completed: boolean) => void
  onDelete: (taskId: string) => void
}

function TaskCard({ task, onToggle, onDelete }: TaskCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive'
      case 'medium': return 'warning'
      case 'low': return 'success'
      default: return 'secondary'
    }
  }

  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && !task.completed

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      {...attributes}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`group relative ${isDragging ? 'opacity-50' : ''}`}
    >
      <Card className={`transition-all duration-200 hover:shadow-md ${
        task.completed ? 'bg-gray-50 border-gray-200' : 'bg-white hover:border-blue-300'
      } ${isOverdue ? 'border-l-4 border-l-red-500' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <button
                onClick={() => onToggle(task.id, task.completed)}
                className={`flex-shrink-0 mt-1 h-5 w-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
                  task.completed
                    ? 'bg-green-600 border-green-600 hover:bg-green-700'
                    : 'border-gray-300 hover:border-blue-500 hover:bg-blue-50'
                }`}
              >
                {task.completed && <CheckIcon className="h-3 w-3 text-white" />}
              </button>
              
              <div className="flex-1 min-w-0">
                <h3 className={`text-sm font-medium transition-all duration-200 ${
                  task.completed ? 'line-through text-gray-500' : 'text-gray-900'
                }`}>
                  {task.title}
                </h3>
                
                {task.description && (
                  <p className={`text-sm mt-1 ${
                    task.completed ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    {task.description}
                  </p>
                )}
                
                <div className="flex items-center mt-2 space-x-2 flex-wrap gap-1">
                  <Badge variant={getPriorityVariant(task.priority)} size="sm">
                    <FlagIcon className="h-3 w-3 mr-1" />
                    {task.priority}
                  </Badge>
                  
                  {task.category && (
                    <Badge variant="outline" size="sm">
                      {task.category}
                    </Badge>
                  )}
                  
                  {task.due_date && (
                    <Badge 
                      variant={isOverdue ? 'destructive' : 'secondary'} 
                      size="sm"
                    >
                      <CalendarIcon className="h-3 w-3 mr-1" />
                      {new Date(task.due_date).toLocaleDateString()}
                    </Badge>
                  )}
                  
                  {isOverdue && (
                    <Badge variant="destructive" size="sm">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      Overdue
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                {...listeners}
                className="p-1 text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing"
              >
                <GripVerticalIcon className="h-4 w-4" />
              </button>
              
              <Button
                onClick={() => onDelete(task.id)}
                variant="ghost"
                size="sm"
                className="text-red-600 hover:text-red-700 hover:bg-red-50 h-8 w-8 p-0"
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

interface DragDropTaskBoardProps {
  tasks: Task[]
  onTasksReorder: (tasks: Task[]) => void
  onToggleTask: (taskId: string, completed: boolean) => void
  onDeleteTask: (taskId: string) => void
}

export default function DragDropTaskBoard({ 
  tasks, 
  onTasksReorder, 
  onToggleTask, 
  onDeleteTask 
}: DragDropTaskBoardProps) {
  const [activeId, setActiveId] = useState<string | null>(null)
  const sensors = useSensors(useSensor(PointerSensor))

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string)
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (active.id !== over?.id) {
      const oldIndex = tasks.findIndex((task) => task.id === active.id)
      const newIndex = tasks.findIndex((task) => task.id === over?.id)
      
      const newTasks = arrayMove(tasks, oldIndex, newIndex)
      onTasksReorder(newTasks)
    }

    setActiveId(null)
  }

  const activeTask = activeId ? tasks.find(task => task.id === activeId) : null

  // Separate completed and pending tasks
  const pendingTasks = tasks.filter(task => !task.completed)
  const completedTasks = tasks.filter(task => task.completed)

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pending Tasks */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                <span>Pending Tasks ({pendingTasks.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <SortableContext 
                items={pendingTasks.map(task => task.id)} 
                strategy={verticalListSortingStrategy}
              >
                <AnimatePresence>
                  <div className="space-y-3">
                    {pendingTasks.map((task) => (
                      <TaskCard
                        key={task.id}
                        task={task}
                        onToggle={onToggleTask}
                        onDelete={onDeleteTask}
                      />
                    ))}
                    {pendingTasks.length === 0 && (
                      <motion.div 
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="text-center py-8 text-gray-500"
                      >
                        No pending tasks. Great job! 🎉
                      </motion.div>
                    )}
                  </div>
                </AnimatePresence>
              </SortableContext>
            </CardContent>
          </Card>
        </div>

        {/* Completed Tasks */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                <span>Completed Tasks ({completedTasks.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <AnimatePresence>
                  {completedTasks.map((task) => (
                    <TaskCard
                      key={task.id}
                      task={task}
                      onToggle={onToggleTask}
                      onDelete={onDeleteTask}
                    />
                  ))}
                  {completedTasks.length === 0 && (
                    <motion.div 
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-8 text-gray-500"
                    >
                      No completed tasks yet.
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <DragOverlay>
        {activeTask ? (
          <TaskCard
            task={activeTask}
            onToggle={() => {}}
            onDelete={() => {}}
          />
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}
