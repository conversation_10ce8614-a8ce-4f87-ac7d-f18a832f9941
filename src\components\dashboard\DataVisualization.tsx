'use client'

import { useState, useEffect } from 'react'
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  LineChart,
  Line,
} from 'recharts'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  DollarSignIcon,
  CalendarIcon,
  TargetIcon,
  ActivityIcon
} from 'lucide-react'
import { motion } from 'framer-motion'

interface DashboardData {
  tasks: {
    total: number
    completed: number
    pending: number
    overdue: number
    completionRate: number
    weeklyProgress: Array<{ day: string; completed: number; created: number }>
  }
  budget: {
    monthlyIncome: number
    monthlyExpenses: number
    netIncome: number
    categoryBreakdown: Array<{ name: string; value: number; color: string }>
    monthlyTrend: Array<{ month: string; income: number; expenses: number }>
  }
  productivity: {
    dailyTasks: Array<{ date: string; completed: number }>
    weeklyGoals: number
    achievedGoals: number
  }
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  icon: React.ElementType
  color: string
  description?: string
}

function MetricCard({ title, value, change, icon: Icon, color, description }: MetricCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="hover:shadow-lg transition-all duration-300 border-l-4" style={{ borderLeftColor: color }}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold text-gray-900">{value}</p>
              {description && (
                <p className="text-xs text-gray-500 mt-1">{description}</p>
              )}
            </div>
            <div className={`p-3 rounded-full`} style={{ backgroundColor: `${color}20` }}>
              <Icon className="h-6 w-6" style={{ color }} />
            </div>
          </div>
          {change !== undefined && (
            <div className="mt-4 flex items-center">
              {change >= 0 ? (
                <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(change)}%
              </span>
              <span className="text-sm text-gray-500 ml-1">vs last month</span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

interface DataVisualizationProps {
  data: DashboardData
}

export default function DataVisualization({ data }: DataVisualizationProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'year'>('week')

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Task Completion Rate"
          value={`${data.tasks.completionRate}%`}
          change={12}
          icon={TargetIcon}
          color="#3B82F6"
          description={`${data.tasks.completed}/${data.tasks.total} tasks`}
        />
        <MetricCard
          title="Monthly Net Income"
          value={`$${data.budget.netIncome.toLocaleString()}`}
          change={data.budget.netIncome >= 0 ? 8 : -5}
          icon={DollarSignIcon}
          color="#10B981"
          description="Income - Expenses"
        />
        <MetricCard
          title="Active Tasks"
          value={data.tasks.pending}
          change={-3}
          icon={ActivityIcon}
          color="#F59E0B"
          description={`${data.tasks.overdue} overdue`}
        />
        <MetricCard
          title="Weekly Goals"
          value={`${data.productivity.achievedGoals}/${data.productivity.weeklyGoals}`}
          change={15}
          icon={CalendarIcon}
          color="#8B5CF6"
          description="Goals achieved"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Task Progress Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Task Progress</CardTitle>
            <CardDescription>Weekly task completion vs creation</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={data.tasks.weeklyProgress}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="completed"
                  stackId="1"
                  stroke="#10B981"
                  fill="#10B981"
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="created"
                  stackId="2"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Budget Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Expense Categories</CardTitle>
            <CardDescription>Monthly spending breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={data.budget.categoryBreakdown}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {data.budget.categoryBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Financial Trend */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Financial Trend</CardTitle>
                <CardDescription>Income vs Expenses over time</CardDescription>
              </div>
              <div className="flex space-x-2">
                {(['week', 'month', 'year'] as const).map((timeframe) => (
                  <Button
                    key={timeframe}
                    variant={selectedTimeframe === timeframe ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedTimeframe(timeframe)}
                  >
                    {timeframe.charAt(0).toUpperCase() + timeframe.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={data.budget.monthlyTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Line
                  type="monotone"
                  dataKey="income"
                  stroke="#10B981"
                  strokeWidth={3}
                  dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="expenses"
                  stroke="#EF4444"
                  strokeWidth={3}
                  dot={{ fill: '#EF4444', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Productivity Heatmap */}
        <Card>
          <CardHeader>
            <CardTitle>Daily Productivity</CardTitle>
            <CardDescription>Tasks completed per day</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={data.productivity.dailyTasks}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="completed" fill="#3B82F6" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Quick Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Insights</CardTitle>
          <CardDescription>AI-powered insights based on your data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="p-4 bg-blue-50 rounded-lg border border-blue-200"
            >
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUpIcon className="h-5 w-5 text-blue-600" />
                <Badge variant="default">Productivity</Badge>
              </div>
              <p className="text-sm text-gray-700">
                You're completing 23% more tasks this week compared to last week. Keep up the great work!
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="p-4 bg-green-50 rounded-lg border border-green-200"
            >
              <div className="flex items-center space-x-2 mb-2">
                <DollarSignIcon className="h-5 w-5 text-green-600" />
                <Badge variant="success">Budget</Badge>
              </div>
              <p className="text-sm text-gray-700">
                Your spending on groceries decreased by 15% this month. You're on track to save $200!
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="p-4 bg-purple-50 rounded-lg border border-purple-200"
            >
              <div className="flex items-center space-x-2 mb-2">
                <TargetIcon className="h-5 w-5 text-purple-600" />
                <Badge variant="purple">Goals</Badge>
              </div>
              <p className="text-sm text-gray-700">
                You're 80% towards your weekly goal. Complete 2 more tasks to achieve it!
              </p>
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
