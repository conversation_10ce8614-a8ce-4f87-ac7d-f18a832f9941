'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { PlusIcon, CheckIcon, XIcon, CalendarIcon, FlagIcon, FilterIcon, SearchIcon, LayoutGridIcon, ListIcon, BarChart3Icon } from 'lucide-react'
import { Database } from '@/lib/types/database'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Input } from '@/components/ui/Input'
import { Loading } from '@/components/ui/Loading'
import { Modal } from '@/components/ui/Modal'
import DragDropTaskBoard from '@/components/tasks/DragDropTaskBoard'
import AdvancedTaskForm from '@/components/forms/AdvancedTaskForm'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'

type Task = Database['public']['Tables']['tasks']['Row']

export default function TasksPage() {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterPriority, setFilterPriority] = useState<'all' | 'low' | 'medium' | 'high'>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'pending'>('all')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'list' | 'board' | 'analytics'>('board')
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    fetchTasks()
  }, [])

  const fetchTasks = async () => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setTasks(data || [])
    } catch (error) {
      console.error('Error fetching tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTaskSubmit = async (formData: any) => {
    setIsSubmitting(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      if (editingTask) {
        // Update existing task
        const { error } = await supabase
          .from('tasks')
          .update({
            title: formData.title,
            description: formData.description || null,
            priority: formData.priority,
            due_date: formData.due_date || null,
            category: formData.category || null,
            estimated_duration: formData.estimated_duration || null,
          })
          .eq('id', editingTask.id)

        if (error) throw error
        toast.success('Task updated successfully!')
      } else {
        // Create new task
        const { error } = await supabase.from('tasks').insert([
          {
            title: formData.title,
            description: formData.description || null,
            priority: formData.priority,
            due_date: formData.due_date || null,
            category: formData.category || null,
            estimated_duration: formData.estimated_duration || null,
            completed: false,
            user_id: user.id,
          },
        ])

        if (error) throw error
        toast.success('Task created successfully!')
      }

      setShowAddForm(false)
      setEditingTask(null)
      fetchTasks()
    } catch (error) {
      console.error('Error saving task:', error)
      toast.error('Failed to save task. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const toggleTask = async (taskId: string, completed: boolean) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ completed: !completed })
        .eq('id', taskId)

      if (error) throw error
      fetchTasks()
    } catch (error) {
      console.error('Error updating task:', error)
    }
  }

  const deleteTask = async (taskId: string) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId)

      if (error) throw error
      toast.success('Task deleted successfully!')
      fetchTasks()
    } catch (error) {
      console.error('Error deleting task:', error)
      toast.error('Failed to delete task. Please try again.')
    }
  }

  const handleTasksReorder = async (reorderedTasks: Task[]) => {
    setTasks(reorderedTasks)
    // You could implement server-side ordering here if needed
  }

  const handleEditTask = (task: Task) => {
    setEditingTask(task)
    setShowAddForm(true)
  }

  const handleCancelForm = () => {
    setShowAddForm(false)
    setEditingTask(null)
  }

  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive'
      case 'medium':
        return 'warning'
      case 'low':
        return 'success'
      default:
        return 'secondary'
    }
  }

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.category?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesPriority = filterPriority === 'all' || task.priority === filterPriority
    const matchesStatus = filterStatus === 'all' ||
                         (filterStatus === 'completed' && task.completed) ||
                         (filterStatus === 'pending' && !task.completed)
    const matchesCategory = filterCategory === 'all' || task.category === filterCategory

    return matchesSearch && matchesPriority && matchesStatus && matchesCategory
  })

  const taskStats = {
    total: tasks.length,
    completed: tasks.filter(t => t.completed).length,
    pending: tasks.filter(t => !t.completed).length,
    overdue: tasks.filter(t => !t.completed && t.due_date && new Date(t.due_date) < new Date()).length,
    highPriority: tasks.filter(t => !t.completed && t.priority === 'high').length,
    dueToday: tasks.filter(t => !t.completed && t.due_date &&
      new Date(t.due_date).toDateString() === new Date().toDateString()).length
  }

  const categories = Array.from(new Set(tasks.map(t => t.category).filter(Boolean))) as string[]

  if (loading) {
    return <Loading size="lg" text="Loading your tasks..." className="h-64" />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Task Management</h1>
          <p className="mt-2 text-gray-600">
            Organize, prioritize, and track your tasks with advanced features
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'board' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('board')}
              className="h-8"
            >
              <LayoutGridIcon className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8"
            >
              <ListIcon className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'analytics' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('analytics')}
              className="h-8"
            >
              <BarChart3Icon className="h-4 w-4" />
            </Button>
          </div>

          <Button onClick={() => setShowAddForm(true)} size="lg">
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Task
          </Button>
        </div>
      </motion.div>

      {/* Enhanced Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-2 lg:grid-cols-6 gap-3 sm:gap-4"
      >
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-blue-600">{taskStats.total}</div>
              <div className="text-xs sm:text-sm text-gray-600">Total Tasks</div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-green-600">{taskStats.completed}</div>
              <div className="text-xs sm:text-sm text-gray-600">Completed</div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-orange-600">{taskStats.pending}</div>
              <div className="text-xs sm:text-sm text-gray-600">Pending</div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-red-500">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-red-600">{taskStats.overdue}</div>
              <div className="text-xs sm:text-sm text-gray-600">Overdue</div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-purple-600">{taskStats.highPriority}</div>
              <div className="text-xs sm:text-sm text-gray-600">High Priority</div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-indigo-500">
          <CardContent className="p-3 sm:p-4">
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-indigo-600">{taskStats.dueToday}</div>
              <div className="text-xs sm:text-sm text-gray-600">Due Today</div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Enhanced Filters and Search */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FilterIcon className="h-5 w-5 text-blue-600" />
              <span>Search & Filter</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search tasks by title, description, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="all">All Priorities</option>
                <option value="high">🔴 High Priority</option>
                <option value="medium">🟡 Medium Priority</option>
                <option value="low">🟢 Low Priority</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="all">All Status</option>
                <option value="pending">⏳ Pending</option>
                <option value="completed">✅ Completed</option>
              </select>

              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="all">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    📁 {category}
                  </option>
                ))}
              </select>

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('')
                  setFilterPriority('all')
                  setFilterStatus('all')
                  setFilterCategory('all')
                }}
                className="flex items-center space-x-2"
              >
                <XIcon className="h-4 w-4" />
                <span>Clear Filters</span>
              </Button>
            </div>

            {/* Active Filters Display */}
            {(searchTerm || filterPriority !== 'all' || filterStatus !== 'all' || filterCategory !== 'all') && (
              <div className="flex flex-wrap gap-2 pt-2 border-t">
                <span className="text-sm text-gray-600">Active filters:</span>
                {searchTerm && (
                  <Badge variant="secondary">Search: "{searchTerm}"</Badge>
                )}
                {filterPriority !== 'all' && (
                  <Badge variant="secondary">Priority: {filterPriority}</Badge>
                )}
                {filterStatus !== 'all' && (
                  <Badge variant="secondary">Status: {filterStatus}</Badge>
                )}
                {filterCategory !== 'all' && (
                  <Badge variant="secondary">Category: {filterCategory}</Badge>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Task Views */}
      <AnimatePresence mode="wait">
        {viewMode === 'board' && (
          <motion.div
            key="board"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <DragDropTaskBoard
              tasks={filteredTasks}
              onTasksReorder={handleTasksReorder}
              onToggleTask={toggleTask}
              onDeleteTask={deleteTask}
            />
          </motion.div>
        )}

        {viewMode === 'list' && (
          <motion.div
            key="list"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Traditional List View */}
            <Card>
              <CardHeader>
                <CardTitle>Task List ({filteredTasks.length})</CardTitle>
                <CardDescription>
                  {filteredTasks.length} of {tasks.length} tasks
                  {searchTerm && ` matching "${searchTerm}"`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredTasks.length === 0 ? (
                  <div className="text-center py-12">
                    <CheckIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-lg font-medium text-gray-900">
                      {tasks.length === 0 ? 'No tasks yet' : 'No matching tasks'}
                    </h3>
                    <p className="mt-1 text-gray-500">
                      {tasks.length === 0
                        ? 'Get started by creating your first task.'
                        : 'Try adjusting your search or filters.'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredTasks.map((task, index) => (
                      <motion.div
                        key={task.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className={`p-4 rounded-lg border transition-all duration-200 hover:shadow-md ${
                          task.completed ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-200 hover:border-blue-300'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <button
                              onClick={() => toggleTask(task.id, task.completed)}
                              className={`flex-shrink-0 mt-1 h-5 w-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
                                task.completed
                                  ? 'bg-green-600 border-green-600 hover:bg-green-700'
                                  : 'border-gray-300 hover:border-blue-500 hover:bg-blue-50'
                              }`}
                            >
                              {task.completed && <CheckIcon className="h-3 w-3 text-white" />}
                            </button>
                            <div className="flex-1 min-w-0">
                              <h3 className={`text-sm font-medium transition-all duration-200 ${
                                task.completed ? 'line-through text-gray-500' : 'text-gray-900'
                              }`}>
                                {task.title}
                              </h3>
                              {task.description && (
                                <p className={`text-sm mt-1 ${
                                  task.completed ? 'text-gray-400' : 'text-gray-600'
                                }`}>
                                  {task.description}
                                </p>
                              )}
                              <div className="flex items-center mt-2 space-x-2 flex-wrap gap-1">
                                <Badge variant={getPriorityVariant(task.priority)} size="sm">
                                  <FlagIcon className="h-3 w-3 mr-1" />
                                  {task.priority}
                                </Badge>
                                {task.category && (
                                  <Badge variant="outline" size="sm">
                                    {task.category}
                                  </Badge>
                                )}
                                {task.due_date && (
                                  <Badge
                                    variant={
                                      !task.completed && new Date(task.due_date) < new Date()
                                        ? 'destructive'
                                        : 'secondary'
                                    }
                                    size="sm"
                                  >
                                    <CalendarIcon className="h-3 w-3 mr-1" />
                                    {new Date(task.due_date).toLocaleDateString()}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              onClick={() => handleEditTask(task)}
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={() => deleteTask(task.id)}
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <XIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}

        {viewMode === 'analytics' && (
          <motion.div
            key="analytics"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Task Analytics</CardTitle>
                <CardDescription>Insights and statistics about your task management</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Completion Rate</h4>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                        style={{ width: `${tasks.length > 0 ? (taskStats.completed / tasks.length) * 100 : 0}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-600">
                      {tasks.length > 0 ? Math.round((taskStats.completed / tasks.length) * 100) : 0}% of tasks completed
                    </p>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Priority Distribution</h4>
                    <div className="space-y-2">
                      {['high', 'medium', 'low'].map((priority) => {
                        const count = tasks.filter(t => t.priority === priority).length
                        const percentage = tasks.length > 0 ? (count / tasks.length) * 100 : 0
                        return (
                          <div key={priority} className="flex items-center justify-between">
                            <span className="text-sm capitalize">{priority} Priority</span>
                            <div className="flex items-center space-x-2">
                              <div className="w-20 bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    priority === 'high' ? 'bg-red-500' :
                                    priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                                  }`}
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-600">{count}</span>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Advanced Task Form Modal */}
      <Modal
        isOpen={showAddForm}
        onClose={handleCancelForm}
        size="lg"
      >
        <AdvancedTaskForm
          onSubmit={handleTaskSubmit}
          onCancel={handleCancelForm}
          initialData={editingTask || undefined}
          isLoading={isSubmitting}
        />
      </Modal>

    </div>
  )
}
