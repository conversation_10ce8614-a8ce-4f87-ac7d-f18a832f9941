{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/budget/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { PlusIcon, DollarSignIcon, TrendingUpIcon, TrendingDownIcon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\n\ntype Transaction = Database['public']['Tables']['transactions']['Row']\ntype BudgetCategory = Database['public']['Tables']['budget_categories']['Row']\n\nexport default function BudgetPage() {\n  const [transactions, setTransactions] = useState<Transaction[]>([])\n  const [categories, setCategories] = useState<BudgetCategory[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddTransaction, setShowAddTransaction] = useState(false)\n  const [showAddCategory, setShowAddCategory] = useState(false)\n  const [newTransaction, setNewTransaction] = useState({\n    amount: '',\n    description: '',\n    date: new Date().toISOString().split('T')[0],\n    type: 'expense' as 'income' | 'expense',\n    category_id: '',\n  })\n  const [newCategory, setNewCategory] = useState({\n    name: '',\n    type: 'expense' as 'income' | 'expense',\n    color: '#3B82F6',\n  })\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchData()\n  }, [])\n\n  const fetchData = async () => {\n    try {\n      const [transactionsResult, categoriesResult] = await Promise.all([\n        supabase\n          .from('transactions')\n          .select('*, budget_categories(name, color)')\n          .order('date', { ascending: false })\n          .limit(50),\n        supabase\n          .from('budget_categories')\n          .select('*')\n          .order('name'),\n      ])\n\n      if (transactionsResult.error) throw transactionsResult.error\n      if (categoriesResult.error) throw categoriesResult.error\n\n      setTransactions(transactionsResult.data || [])\n      setCategories(categoriesResult.data || [])\n    } catch (error) {\n      console.error('Error fetching data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addTransaction = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('transactions').insert([\n        {\n          ...newTransaction,\n          amount: parseFloat(newTransaction.amount),\n          user_id: user.id,\n          category_id: newTransaction.category_id || null,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewTransaction({\n        amount: '',\n        description: '',\n        date: new Date().toISOString().split('T')[0],\n        type: 'expense',\n        category_id: '',\n      })\n      setShowAddTransaction(false)\n      fetchData()\n    } catch (error) {\n      console.error('Error adding transaction:', error)\n    }\n  }\n\n  const addCategory = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('budget_categories').insert([\n        {\n          ...newCategory,\n          user_id: user.id,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewCategory({\n        name: '',\n        type: 'expense',\n        color: '#3B82F6',\n      })\n      setShowAddCategory(false)\n      fetchData()\n    } catch (error) {\n      console.error('Error adding category:', error)\n    }\n  }\n\n  const calculateTotals = () => {\n    const currentMonth = new Date().toISOString().slice(0, 7)\n    const monthlyTransactions = transactions.filter(t => \n      t.date.startsWith(currentMonth)\n    )\n\n    const income = monthlyTransactions\n      .filter(t => t.type === 'income')\n      .reduce((sum, t) => sum + t.amount, 0)\n\n    const expenses = monthlyTransactions\n      .filter(t => t.type === 'expense')\n      .reduce((sum, t) => sum + t.amount, 0)\n\n    return { income, expenses, net: income - expenses }\n  }\n\n  const totals = calculateTotals()\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div>\n      <div className=\"mb-8 flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Budget</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Track your income and expenses\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={() => setShowAddCategory(true)}\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            Add Category\n          </button>\n          <button\n            onClick={() => setShowAddTransaction(true)}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Add Transaction\n          </button>\n        </div>\n      </div>\n\n      {/* Monthly Summary */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-3 mb-8\">\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <TrendingUpIcon className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Monthly Income\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    ${totals.income.toFixed(2)}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <TrendingDownIcon className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Monthly Expenses\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    ${totals.expenses.toFixed(2)}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <DollarSignIcon className={`h-6 w-6 ${totals.net >= 0 ? 'text-green-600' : 'text-red-600'}`} />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Net Income\n                  </dt>\n                  <dd className={`text-lg font-medium ${totals.net >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                    ${totals.net.toFixed(2)}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Add Category Form */}\n      {showAddCategory && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add Category</h3>\n          <form onSubmit={addCategory}>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n              <div>\n                <label htmlFor=\"categoryName\" className=\"block text-sm font-medium text-gray-700\">\n                  Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"categoryName\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newCategory.name}\n                  onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"categoryType\" className=\"block text-sm font-medium text-gray-700\">\n                  Type\n                </label>\n                <select\n                  id=\"categoryType\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newCategory.type}\n                  onChange={(e) => setNewCategory({ ...newCategory, type: e.target.value as 'income' | 'expense' })}\n                >\n                  <option value=\"expense\">Expense</option>\n                  <option value=\"income\">Income</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"categoryColor\" className=\"block text-sm font-medium text-gray-700\">\n                  Color\n                </label>\n                <input\n                  type=\"color\"\n                  id=\"categoryColor\"\n                  className=\"mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500\"\n                  value={newCategory.color}\n                  onChange={(e) => setNewCategory({ ...newCategory, color: e.target.value })}\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddCategory(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                Add Category\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Add Transaction Form */}\n      {showAddTransaction && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add Transaction</h3>\n          <form onSubmit={addTransaction}>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n              <div>\n                <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700\">\n                  Amount\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"amount\"\n                  step=\"0.01\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.amount}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, amount: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"type\" className=\"block text-sm font-medium text-gray-700\">\n                  Type\n                </label>\n                <select\n                  id=\"type\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.type}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, type: e.target.value as 'income' | 'expense' })}\n                >\n                  <option value=\"expense\">Expense</option>\n                  <option value=\"income\">Income</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700\">\n                  Category\n                </label>\n                <select\n                  id=\"category\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.category_id}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, category_id: e.target.value })}\n                >\n                  <option value=\"\">No category</option>\n                  {categories\n                    .filter(cat => cat.type === newTransaction.type)\n                    .map((category) => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"date\" className=\"block text-sm font-medium text-gray-700\">\n                  Date\n                </label>\n                <input\n                  type=\"date\"\n                  id=\"date\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.date}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, date: e.target.value })}\n                />\n              </div>\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n                  Description\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"description\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.description}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddTransaction(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                Add Transaction\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Recent Transactions */}\n      <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n        <div className=\"px-4 py-5 sm:px-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n            Recent Transactions\n          </h3>\n        </div>\n        {transactions.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <DollarSignIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No transactions</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">Get started by adding your first transaction.</p>\n          </div>\n        ) : (\n          <ul className=\"divide-y divide-gray-200\">\n            {transactions.map((transaction) => (\n              <li key={transaction.id} className=\"px-6 py-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <div className={`flex-shrink-0 h-3 w-3 rounded-full`} \n                         style={{ backgroundColor: (transaction as any).budget_categories?.color || '#6B7280' }} />\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {transaction.description}\n                      </p>\n                      <p className=\"text-sm text-gray-500\">\n                        {new Date(transaction.date).toLocaleDateString()} • {(transaction as any).budget_categories?.name || 'No category'}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className={`text-sm font-medium ${\n                      transaction.type === 'income' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {transaction.type === 'income' ? '+' : '-'}${transaction.amount.toFixed(2)}\n                    </span>\n                  </div>\n                </div>\n              </li>\n            ))}\n          </ul>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,QAAQ;QACR,aAAa;QACb,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,MAAM;QACN,aAAa;IACf;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,CAAC,oBAAoB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC/D,SACG,IAAI,CAAC,gBACL,MAAM,CAAC,qCACP,KAAK,CAAC,QAAQ;oBAAE,WAAW;gBAAM,GACjC,KAAK,CAAC;gBACT,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC;aACV;YAED,IAAI,mBAAmB,KAAK,EAAE,MAAM,mBAAmB,KAAK;YAC5D,IAAI,iBAAiB,KAAK,EAAE,MAAM,iBAAiB,KAAK;YAExD,gBAAgB,mBAAmB,IAAI,IAAI,EAAE;YAC7C,cAAc,iBAAiB,IAAI,IAAI,EAAE;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC;gBAC3D;oBACE,GAAG,cAAc;oBACjB,QAAQ,WAAW,eAAe,MAAM;oBACxC,SAAS,KAAK,EAAE;oBAChB,aAAa,eAAe,WAAW,IAAI;gBAC7C;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,kBAAkB;gBAChB,QAAQ;gBACR,aAAa;gBACb,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC5C,MAAM;gBACN,aAAa;YACf;YACA,sBAAsB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC;gBAChE;oBACE,GAAG,WAAW;oBACd,SAAS,KAAK,EAAE;gBAClB;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,eAAe;gBACb,MAAM;gBACN,MAAM;gBACN,OAAO;YACT;YACA,mBAAmB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG;QACvD,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAA,IAC9C,EAAE,IAAI,CAAC,UAAU,CAAC;QAGpB,MAAM,SAAS,oBACZ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,MAAM,WAAW,oBACd,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,OAAO;YAAE;YAAQ;YAAU,KAAK,SAAS;QAAS;IACpD;IAEA,MAAM,SAAS;IAEf,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,sBAAsB;gCACrC,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;;wDAAoC;wDAC9C,OAAO,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;;wDAAoC;wDAC9C,OAAO,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,GAAG,IAAI,IAAI,mBAAmB,gBAAgB;;;;;;;;;;;kDAE7F,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAW,CAAC,oBAAoB,EAAE,OAAO,GAAG,IAAI,IAAI,mBAAmB,gBAAgB;;wDAAE;wDACzF,OAAO,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAK,UAAU;;0CACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA0C;;;;;;0DAGlF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAG3E,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA0C;;;;;;0DAGlF,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAyB;;kEAE/F,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAG3B,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAA0C;;;;;;0DAGnF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;;;;;;;0CAI9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;YASR,oCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAK,UAAU;;0CACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAS,WAAU;0DAA0C;;;;;;0DAG5E,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,MAAM;gDAC5B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGnF,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA0C;;;;;;0DAG1E,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,eAAe,IAAI;gDAC1B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAyB;;kEAErG,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAG3B,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA0C;;;;;;0DAG9E,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,eAAe,WAAW;gDACjC,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;;kEAEpF,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,WACE,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,eAAe,IAAI,EAC9C,GAAG,CAAC,CAAC,yBACJ,6LAAC;4DAAyB,OAAO,SAAS,EAAE;sEACzC,SAAS,IAAI;2DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;kDAMhC,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA0C;;;;;;0DAG1E,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,IAAI;gDAC1B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGjF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA0C;;;;;;0DAGjF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,WAAW;gDACjC,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;;;;;;;0CAI1F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAA8C;;;;;;;;;;;oBAI7D,aAAa,MAAM,KAAK,kBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;0CAC1B,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;6CAG5C,6LAAC;wBAAG,WAAU;kCACX,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;gCAAwB,WAAU;0CACjC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,kCAAkC,CAAC;oDAC/C,OAAO;wDAAE,iBAAiB,AAAC,YAAoB,iBAAiB,EAAE,SAAS;oDAAU;;;;;;8DAC1F,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,YAAY,WAAW;;;;;;sEAE1B,6LAAC;4DAAE,WAAU;;gEACV,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB;gEAAG;gEAAK,YAAoB,iBAAiB,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;sDAI3G,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,CAAC,oBAAoB,EACpC,YAAY,IAAI,KAAK,WAAW,mBAAmB,gBACnD;;oDACC,YAAY,IAAI,KAAK,WAAW,MAAM;oDAAI;oDAAE,YAAY,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;+BAlBvE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;AA6BrC;GAlbwB;KAAA", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1079, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "file": "trending-down.js", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/lucide-react/src/icons/trending-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 17h6v-6', key: 't6n2it' }],\n  ['path', { d: 'm22 17-8.5-8.5-5 5L2 7', key: 'x473p' }],\n];\n\n/**\n * @component @name TrendingDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTdoNnYtNiIgLz4KICA8cGF0aCBkPSJtMjIgMTctOC41LTguNS01IDVMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trending-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingDown = createLucideIcon('trending-down', __iconNode);\n\nexport default TrendingDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}