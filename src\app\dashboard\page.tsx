import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import {
  CheckSquareIcon,
  DollarSignIcon,
  ShoppingCartIcon,
  ChefHatIcon,
} from 'lucide-react'

export default async function DashboardPage() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user stats
  const { data: stats } = await supabase.rpc('get_user_stats', {
    user_uuid: user.id,
  })

  const quickStats = [
    {
      name: 'Total Tasks',
      value: stats?.total_tasks || 0,
      icon: CheckSquareIcon,
      color: 'bg-blue-500',
    },
    {
      name: 'Monthly Income',
      value: `$${stats?.monthly_income || 0}`,
      icon: DollarSignIcon,
      color: 'bg-green-500',
    },
    {
      name: 'Shopping Lists',
      value: stats?.total_shopping_lists || 0,
      icon: ShoppingCartIcon,
      color: 'bg-purple-500',
    },
    {
      name: 'Recipes',
      value: stats?.total_recipes || 0,
      icon: ChefHatIcon,
      color: 'bg-orange-500',
    },
  ]

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-600">
          Welcome back! Here's what's happening with your life management.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {quickStats.map((stat) => (
          <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-md ${stat.color}`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Tasks */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Tasks
            </h3>
            <div className="text-sm text-gray-500">
              <p>No recent tasks. <a href="/tasks" className="text-blue-600 hover:text-blue-500">Create your first task</a></p>
            </div>
          </div>
        </div>

        {/* Budget Overview */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Budget Overview
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Monthly Income</span>
                <span className="text-sm font-medium text-green-600">
                  ${stats?.monthly_income || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Monthly Expenses</span>
                <span className="text-sm font-medium text-red-600">
                  ${stats?.monthly_expenses || 0}
                </span>
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-900">Net</span>
                  <span className={`text-sm font-medium ${
                    (stats?.monthly_income || 0) - (stats?.monthly_expenses || 0) >= 0 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    ${(stats?.monthly_income || 0) - (stats?.monthly_expenses || 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
