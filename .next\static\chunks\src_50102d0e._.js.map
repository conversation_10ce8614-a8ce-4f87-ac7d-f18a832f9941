{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse'\n  className?: string\n  text?: string\n}\n\nconst sizeClasses = {\n  sm: 'h-4 w-4',\n  md: 'h-6 w-6',\n  lg: 'h-8 w-8',\n  xl: 'h-12 w-12',\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce\"></div>\n        </div>\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  return null\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('animate-pulse', className)}>\n      <div className=\"bg-gray-200 rounded-lg h-4 w-full\"></div>\n    </div>\n  )\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-6 animate-pulse\">\n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n        </div>\n        <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingTable({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {Array.from({ length: rows }).map((_, i) => (\n          <div key={i} className=\"p-4 animate-pulse\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"h-4 w-4 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingStats() {\n  return (\n    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-4 animate-pulse\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-gray-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mx-auto\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AASA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS;IACb,IAAI,YAAY,WAAW;QACzB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;gBAEhB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,OAAO;AACT;KAhDgB;AAkDT,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MANgB;AAQT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;MAbgB;AAeT,SAAS,aAAa,EAAE,OAAO,CAAC,EAAqB;IAC1D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;;;;;;;;AAcpB;MAtBgB;AAwBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gBAAY,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;eAHT;;;;;;;;;;AASlB;MAbgB", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/budget/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { PlusIcon, DollarSignIcon, TrendingUpIcon, TrendingDownIcon, SearchIcon, FilterIcon, EditIcon, TrashIcon, CalendarIcon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { Input } from '@/components/ui/Input'\nimport { Loading } from '@/components/ui/Loading'\nimport { Modal } from '@/components/ui/Modal'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport toast from 'react-hot-toast'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\n\ntype Transaction = Database['public']['Tables']['transactions']['Row']\ntype BudgetCategory = Database['public']['Tables']['budget_categories']['Row']\n\nconst transactionSchema = z.object({\n  amount: z.number().min(0.01, 'Amount must be greater than 0'),\n  description: z.string().min(1, 'Description is required').max(200, 'Description too long'),\n  category_id: z.string().min(1, 'Category is required'),\n  type: z.enum(['income', 'expense']),\n  date: z.string().min(1, 'Date is required'),\n})\n\nconst categorySchema = z.object({\n  name: z.string().min(1, 'Category name is required').max(50, 'Name too long'),\n  budget_limit: z.number().min(0, 'Budget limit must be positive').optional(),\n  color: z.string().optional(),\n})\n\ntype TransactionFormData = z.infer<typeof transactionSchema>\ntype CategoryFormData = z.infer<typeof categorySchema>\n\nexport default function BudgetPage() {\n  const [transactions, setTransactions] = useState<Transaction[]>([])\n  const [categories, setCategories] = useState<Category[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddTransaction, setShowAddTransaction] = useState(false)\n  const [showAddCategory, setShowAddCategory] = useState(false)\n  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null)\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filterType, setFilterType] = useState<'all' | 'income' | 'expense'>('all')\n  const [filterCategory, setFilterCategory] = useState<string>('all')\n  const [dateRange, setDateRange] = useState({\n    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  })\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchData()\n  }, [])\n\n  const fetchData = async () => {\n    try {\n      const [transactionsResult, categoriesResult] = await Promise.all([\n        supabase\n          .from('transactions')\n          .select('*, budget_categories(name, color)')\n          .order('date', { ascending: false })\n          .limit(50),\n        supabase\n          .from('budget_categories')\n          .select('*')\n          .order('name'),\n      ])\n\n      if (transactionsResult.error) throw transactionsResult.error\n      if (categoriesResult.error) throw categoriesResult.error\n\n      setTransactions(transactionsResult.data || [])\n      setCategories(categoriesResult.data || [])\n    } catch (error) {\n      console.error('Error fetching data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addTransaction = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('transactions').insert([\n        {\n          ...newTransaction,\n          amount: parseFloat(newTransaction.amount),\n          user_id: user.id,\n          category_id: newTransaction.category_id || null,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewTransaction({\n        amount: '',\n        description: '',\n        date: new Date().toISOString().split('T')[0],\n        type: 'expense',\n        category_id: '',\n      })\n      setShowAddTransaction(false)\n      fetchData()\n    } catch (error) {\n      console.error('Error adding transaction:', error)\n    }\n  }\n\n  const addCategory = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('budget_categories').insert([\n        {\n          ...newCategory,\n          user_id: user.id,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewCategory({\n        name: '',\n        type: 'expense',\n        color: '#3B82F6',\n      })\n      setShowAddCategory(false)\n      fetchData()\n    } catch (error) {\n      console.error('Error adding category:', error)\n    }\n  }\n\n  const calculateTotals = () => {\n    const currentMonth = new Date().toISOString().slice(0, 7)\n    const monthlyTransactions = transactions.filter(t => \n      t.date.startsWith(currentMonth)\n    )\n\n    const income = monthlyTransactions\n      .filter(t => t.type === 'income')\n      .reduce((sum, t) => sum + t.amount, 0)\n\n    const expenses = monthlyTransactions\n      .filter(t => t.type === 'expense')\n      .reduce((sum, t) => sum + t.amount, 0)\n\n    return { income, expenses, net: income - expenses }\n  }\n\n  const totals = calculateTotals()\n\n  if (loading) {\n    return <Loading size=\"lg\" text=\"Loading your budget data...\" className=\"h-64\" />\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Budget</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Track your income and expenses with detailed insights\n          </p>\n        </div>\n        <div className=\"flex flex-col sm:flex-row gap-3\">\n          <Button\n            onClick={() => setShowAddCategory(true)}\n            variant=\"outline\"\n          >\n            Add Category\n          </Button>\n          <Button\n            onClick={() => setShowAddTransaction(true)}\n            size=\"lg\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Add Transaction\n          </Button>\n        </div>\n      </div>\n\n      {/* Monthly Summary */}\n      <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n        <Card className=\"bg-gradient-to-br from-green-50 to-emerald-50 border-green-200\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-green-700\">Monthly Income</p>\n                <p className=\"text-2xl font-bold text-green-900\">\n                  ${totals.income.toFixed(2)}\n                </p>\n              </div>\n              <div className=\"p-3 bg-green-100 rounded-full\">\n                <TrendingUpIcon className=\"h-6 w-6 text-green-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-gradient-to-br from-red-50 to-rose-50 border-red-200\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-red-700\">Monthly Expenses</p>\n                <p className=\"text-2xl font-bold text-red-900\">\n                  ${totals.expenses.toFixed(2)}\n                </p>\n              </div>\n              <div className=\"p-3 bg-red-100 rounded-full\">\n                <TrendingDownIcon className=\"h-6 w-6 text-red-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className={`bg-gradient-to-br ${\n          totals.net >= 0\n            ? 'from-blue-50 to-indigo-50 border-blue-200'\n            : 'from-orange-50 to-red-50 border-orange-200'\n        }`}>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className={`text-sm font-medium ${\n                  totals.net >= 0 ? 'text-blue-700' : 'text-orange-700'\n                }`}>\n                  Net Income\n                </p>\n                <p className={`text-2xl font-bold ${\n                  totals.net >= 0 ? 'text-blue-900' : 'text-orange-900'\n                }`}>\n                  ${totals.net.toFixed(2)}\n                </p>\n              </div>\n              <div className={`p-3 rounded-full ${\n                totals.net >= 0 ? 'bg-blue-100' : 'bg-orange-100'\n              }`}>\n                <DollarSignIcon className={`h-6 w-6 ${\n                  totals.net >= 0 ? 'text-blue-600' : 'text-orange-600'\n                }`} />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Add Category Form */}\n      {showAddCategory && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add Category</h3>\n          <form onSubmit={addCategory}>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n              <div>\n                <label htmlFor=\"categoryName\" className=\"block text-sm font-medium text-gray-700\">\n                  Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"categoryName\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newCategory.name}\n                  onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"categoryType\" className=\"block text-sm font-medium text-gray-700\">\n                  Type\n                </label>\n                <select\n                  id=\"categoryType\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newCategory.type}\n                  onChange={(e) => setNewCategory({ ...newCategory, type: e.target.value as 'income' | 'expense' })}\n                >\n                  <option value=\"expense\">Expense</option>\n                  <option value=\"income\">Income</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"categoryColor\" className=\"block text-sm font-medium text-gray-700\">\n                  Color\n                </label>\n                <input\n                  type=\"color\"\n                  id=\"categoryColor\"\n                  className=\"mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500\"\n                  value={newCategory.color}\n                  onChange={(e) => setNewCategory({ ...newCategory, color: e.target.value })}\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddCategory(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                Add Category\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Add Transaction Form */}\n      {showAddTransaction && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add Transaction</h3>\n          <form onSubmit={addTransaction}>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n              <div>\n                <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700\">\n                  Amount\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"amount\"\n                  step=\"0.01\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.amount}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, amount: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"type\" className=\"block text-sm font-medium text-gray-700\">\n                  Type\n                </label>\n                <select\n                  id=\"type\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.type}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, type: e.target.value as 'income' | 'expense' })}\n                >\n                  <option value=\"expense\">Expense</option>\n                  <option value=\"income\">Income</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700\">\n                  Category\n                </label>\n                <select\n                  id=\"category\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.category_id}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, category_id: e.target.value })}\n                >\n                  <option value=\"\">No category</option>\n                  {categories\n                    .filter(cat => cat.type === newTransaction.type)\n                    .map((category) => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"date\" className=\"block text-sm font-medium text-gray-700\">\n                  Date\n                </label>\n                <input\n                  type=\"date\"\n                  id=\"date\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.date}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, date: e.target.value })}\n                />\n              </div>\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n                  Description\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"description\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.description}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddTransaction(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                Add Transaction\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Recent Transactions */}\n      <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n        <div className=\"px-4 py-5 sm:px-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n            Recent Transactions\n          </h3>\n        </div>\n        {transactions.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <DollarSignIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No transactions</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">Get started by adding your first transaction.</p>\n          </div>\n        ) : (\n          <ul className=\"divide-y divide-gray-200\">\n            {transactions.map((transaction) => (\n              <li key={transaction.id} className=\"px-6 py-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <div className={`flex-shrink-0 h-3 w-3 rounded-full`} \n                         style={{ backgroundColor: (transaction as any).budget_categories?.color || '#6B7280' }} />\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {transaction.description}\n                      </p>\n                      <p className=\"text-sm text-gray-500\">\n                        {new Date(transaction.date).toLocaleDateString()} • {(transaction as any).budget_categories?.name || 'No category'}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className={`text-sm font-medium ${\n                      transaction.type === 'income' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {transaction.type === 'income' ? '+' : '-'}${transaction.amount.toFixed(2)}\n                    </span>\n                  </div>\n                </div>\n              </li>\n            ))}\n          </ul>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAGA;AAMA;;;AAhBA;;;;;;;;AAqBA,MAAM,oBAAoB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,QAAQ,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM;IAC7B,aAAa,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,KAAK;IACnE,aAAa,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,MAAM,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;IAClC,MAAM,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC1B;AAEA,MAAM,iBAAiB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,IAAI;IAC7D,cAAc,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,iCAAiC,QAAQ;IACzE,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAKe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACjF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,OAAO,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI,GAAG,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/F,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,CAAC,oBAAoB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC/D,SACG,IAAI,CAAC,gBACL,MAAM,CAAC,qCACP,KAAK,CAAC,QAAQ;oBAAE,WAAW;gBAAM,GACjC,KAAK,CAAC;gBACT,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC;aACV;YAED,IAAI,mBAAmB,KAAK,EAAE,MAAM,mBAAmB,KAAK;YAC5D,IAAI,iBAAiB,KAAK,EAAE,MAAM,iBAAiB,KAAK;YAExD,gBAAgB,mBAAmB,IAAI,IAAI,EAAE;YAC7C,cAAc,iBAAiB,IAAI,IAAI,EAAE;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC;gBAC3D;oBACE,GAAG,cAAc;oBACjB,QAAQ,WAAW,eAAe,MAAM;oBACxC,SAAS,KAAK,EAAE;oBAChB,aAAa,eAAe,WAAW,IAAI;gBAC7C;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,kBAAkB;gBAChB,QAAQ;gBACR,aAAa;gBACb,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC5C,MAAM;gBACN,aAAa;YACf;YACA,sBAAsB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC;gBAChE;oBACE,GAAG,WAAW;oBACd,SAAS,KAAK,EAAE;gBAClB;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,eAAe;gBACb,MAAM;gBACN,MAAM;gBACN,OAAO;YACT;YACA,mBAAmB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG;QACvD,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAA,IAC9C,EAAE,IAAI,CAAC,UAAU,CAAC;QAGpB,MAAM,SAAS,oBACZ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,MAAM,WAAW,oBACd,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,OAAO;YAAE;YAAQ;YAAU,KAAK,SAAS;QAAS;IACpD;IAEA,MAAM,SAAS;IAEf,IAAI,SAAS;QACX,qBAAO,6LAAC,sIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;YAA8B,WAAU;;;;;;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,mBAAmB;gCAClC,SAAQ;0CACT;;;;;;0CAGD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,sBAAsB;gCACrC,MAAK;;kDAEL,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;;oDAAoC;oDAC7C,OAAO,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAG5B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;0DAChD,6LAAC;gDAAE,WAAU;;oDAAkC;oDAC3C,OAAO,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAG9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAW,CAAC,kBAAkB,EAClC,OAAO,GAAG,IAAI,IACV,8CACA,8CACJ;kCACA,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAW,CAAC,oBAAoB,EACjC,OAAO,GAAG,IAAI,IAAI,kBAAkB,mBACpC;0DAAE;;;;;;0DAGJ,6LAAC;gDAAE,WAAW,CAAC,mBAAmB,EAChC,OAAO,GAAG,IAAI,IAAI,kBAAkB,mBACpC;;oDAAE;oDACA,OAAO,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAW,CAAC,iBAAiB,EAChC,OAAO,GAAG,IAAI,IAAI,gBAAgB,iBAClC;kDACA,cAAA,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,WAAW,CAAC,QAAQ,EAClC,OAAO,GAAG,IAAI,IAAI,kBAAkB,mBACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQX,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAK,UAAU;;0CACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA0C;;;;;;0DAGlF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAG3E,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA0C;;;;;;0DAGlF,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAyB;;kEAE/F,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAG3B,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAA0C;;;;;;0DAGnF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;;;;;;;0CAI9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;YASR,oCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAK,UAAU;;0CACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAS,WAAU;0DAA0C;;;;;;0DAG5E,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,MAAM;gDAC5B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGnF,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA0C;;;;;;0DAG1E,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,eAAe,IAAI;gDAC1B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAyB;;kEAErG,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAG3B,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA0C;;;;;;0DAG9E,6LAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,eAAe,WAAW;gDACjC,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;;kEAEpF,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,WACE,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,eAAe,IAAI,EAC9C,GAAG,CAAC,CAAC,yBACJ,6LAAC;4DAAyB,OAAO,SAAS,EAAE;sEACzC,SAAS,IAAI;2DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;kDAMhC,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA0C;;;;;;0DAG1E,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,IAAI;gDAC1B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGjF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA0C;;;;;;0DAGjF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,WAAW;gDACjC,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;;;;;;;0CAI1F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAA8C;;;;;;;;;;;oBAI7D,aAAa,MAAM,KAAK,kBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;0CAC1B,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;6CAG5C,6LAAC;wBAAG,WAAU;kCACX,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;gCAAwB,WAAU;0CACjC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,kCAAkC,CAAC;oDAC/C,OAAO;wDAAE,iBAAiB,AAAC,YAAoB,iBAAiB,EAAE,SAAS;oDAAU;;;;;;8DAC1F,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,YAAY,WAAW;;;;;;sEAE1B,6LAAC;4DAAE,WAAU;;gEACV,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB;gEAAG;gEAAK,YAAoB,iBAAiB,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;sDAI3G,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,CAAC,oBAAoB,EACpC,YAAY,IAAI,KAAK,WAAW,mBAAmB,gBACnD;;oDACC,YAAY,IAAI,KAAK,WAAW,MAAM;oDAAI;oDAAE,YAAY,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;+BAlBvE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;AA6BrC;GA/awB;KAAA", "debugId": null}}]}