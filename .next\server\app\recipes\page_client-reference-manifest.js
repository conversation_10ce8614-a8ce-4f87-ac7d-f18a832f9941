globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/recipes/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/src/components/providers/ToastProvider.tsx <module evaluation>":{"id":"[project]/src/components/providers/ToastProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_2e4dcf2e._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/providers/ToastProvider.tsx":{"id":"[project]/src/components/providers/ToastProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_2e4dcf2e._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/layout/AuthenticatedLayout.tsx <module evaluation>":{"id":"[project]/src/components/layout/AuthenticatedLayout.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_2e4dcf2e._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_%40supabase_node-fetch_browser_5904100a.js","/_next/static/chunks/node_modules_44be2902._.js","/_next/static/chunks/src_df79ced5._.js","/_next/static/chunks/src_app_recipes_layout_tsx_651d4878._.js"],"async":false},"[project]/src/components/layout/AuthenticatedLayout.tsx":{"id":"[project]/src/components/layout/AuthenticatedLayout.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_2e4dcf2e._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_%40supabase_node-fetch_browser_5904100a.js","/_next/static/chunks/node_modules_44be2902._.js","/_next/static/chunks/src_df79ced5._.js","/_next/static/chunks/src_app_recipes_layout_tsx_651d4878._.js"],"async":false},"[project]/src/app/recipes/page.tsx <module evaluation>":{"id":"[project]/src/app/recipes/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_2e4dcf2e._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_%40supabase_node-fetch_browser_5904100a.js","/_next/static/chunks/node_modules_44be2902._.js","/_next/static/chunks/src_df79ced5._.js","/_next/static/chunks/src_app_recipes_layout_tsx_651d4878._.js","/_next/static/chunks/_0b4f24e3._.js","/_next/static/chunks/src_app_recipes_page_tsx_44c73be2._.js"],"async":false},"[project]/src/app/recipes/page.tsx":{"id":"[project]/src/app/recipes/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_2e4dcf2e._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_%40supabase_node-fetch_browser_5904100a.js","/_next/static/chunks/node_modules_44be2902._.js","/_next/static/chunks/src_df79ced5._.js","/_next/static/chunks/src_app_recipes_layout_tsx_651d4878._.js","/_next/static/chunks/_0b4f24e3._.js","/_next/static/chunks/src_app_recipes_page_tsx_44c73be2._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/src/components/providers/ToastProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/ToastProvider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d17816b3._.js"],"async":false}},"[project]/src/components/layout/AuthenticatedLayout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/layout/AuthenticatedLayout.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d17816b3._.js","server/chunks/ssr/[root-of-the-server]__98461acf._.js","server/chunks/ssr/node_modules_next_8e5997c7._.js","server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js","server/chunks/ssr/node_modules_ws_28df0e43._.js","server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_080f843b._.js","server/chunks/ssr/node_modules_39cf5374._.js","server/chunks/ssr/[root-of-the-server]__2ff83f18._.js"],"async":false}},"[project]/src/app/recipes/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/recipes/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d17816b3._.js","server/chunks/ssr/[root-of-the-server]__98461acf._.js","server/chunks/ssr/node_modules_next_8e5997c7._.js","server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js","server/chunks/ssr/node_modules_ws_28df0e43._.js","server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_080f843b._.js","server/chunks/ssr/node_modules_39cf5374._.js","server/chunks/ssr/[root-of-the-server]__2ff83f18._.js","server/chunks/ssr/_74e6d6f7._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/src/components/providers/ToastProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/ToastProvider.tsx (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/src/components/layout/AuthenticatedLayout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/layout/AuthenticatedLayout.tsx (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}},"[project]/src/app/recipes/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/recipes/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/recipes/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__8ebb6d4b._.css","inlined":false}],"[project]/src/app/recipes/layout":[{"path":"static/chunks/[root-of-the-server]__8ebb6d4b._.css","inlined":false}],"[project]/src/app/recipes/page":[{"path":"static/chunks/[root-of-the-server]__8ebb6d4b._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"[project]/src/app/layout":["static/chunks/_2e4dcf2e._.js","static/chunks/src_app_layout_tsx_007ca514._.js"],"[project]/src/app/recipes/layout":["static/chunks/_2e4dcf2e._.js","static/chunks/src_app_layout_tsx_007ca514._.js","static/chunks/node_modules_@supabase_node-fetch_browser_5904100a.js","static/chunks/node_modules_44be2902._.js","static/chunks/src_df79ced5._.js","static/chunks/src_app_recipes_layout_tsx_651d4878._.js"],"[project]/src/app/recipes/page":["static/chunks/_2e4dcf2e._.js","static/chunks/src_app_layout_tsx_007ca514._.js","static/chunks/node_modules_@supabase_node-fetch_browser_5904100a.js","static/chunks/node_modules_44be2902._.js","static/chunks/src_df79ced5._.js","static/chunks/src_app_recipes_layout_tsx_651d4878._.js","static/chunks/_0b4f24e3._.js","static/chunks/src_app_recipes_page_tsx_44c73be2._.js"]}}
