{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/tasks/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { PlusIcon, CheckIcon, XIcon, CalendarIcon, FlagIcon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\n\ntype Task = Database['public']['Tables']['tasks']['Row']\n\nexport default function TasksPage() {\n  const [tasks, setTasks] = useState<Task[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [newTask, setNewTask] = useState({\n    title: '',\n    description: '',\n    priority: 'medium' as 'low' | 'medium' | 'high',\n    due_date: '',\n    category: '',\n  })\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchTasks()\n  }, [])\n\n  const fetchTasks = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('tasks')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setTasks(data || [])\n    } catch (error) {\n      console.error('Error fetching tasks:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addTask = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('tasks').insert([\n        {\n          ...newTask,\n          user_id: user.id,\n          due_date: newTask.due_date || null,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewTask({\n        title: '',\n        description: '',\n        priority: 'medium',\n        due_date: '',\n        category: '',\n      })\n      setShowAddForm(false)\n      fetchTasks()\n    } catch (error) {\n      console.error('Error adding task:', error)\n    }\n  }\n\n  const toggleTask = async (taskId: string, completed: boolean) => {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .update({ completed: !completed })\n        .eq('id', taskId)\n\n      if (error) throw error\n      fetchTasks()\n    } catch (error) {\n      console.error('Error updating task:', error)\n    }\n  }\n\n  const deleteTask = async (taskId: string) => {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .delete()\n        .eq('id', taskId)\n\n      if (error) throw error\n      fetchTasks()\n    } catch (error) {\n      console.error('Error deleting task:', error)\n    }\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-600 bg-red-100'\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100'\n      case 'low':\n        return 'text-green-600 bg-green-100'\n      default:\n        return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div>\n      <div className=\"mb-8 flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Tasks</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Manage your tasks and stay organized\n          </p>\n        </div>\n        <button\n          onClick={() => setShowAddForm(true)}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n        >\n          <PlusIcon className=\"h-4 w-4 mr-2\" />\n          Add Task\n        </button>\n      </div>\n\n      {/* Add Task Form */}\n      {showAddForm && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <form onSubmit={addTask}>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700\">\n                  Title\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.title}\n                  onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}\n                />\n              </div>\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n                  Description\n                </label>\n                <textarea\n                  id=\"description\"\n                  rows={3}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.description}\n                  onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"priority\" className=\"block text-sm font-medium text-gray-700\">\n                  Priority\n                </label>\n                <select\n                  id=\"priority\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.priority}\n                  onChange={(e) => setNewTask({ ...newTask, priority: e.target.value as 'low' | 'medium' | 'high' })}\n                >\n                  <option value=\"low\">Low</option>\n                  <option value=\"medium\">Medium</option>\n                  <option value=\"high\">High</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"due_date\" className=\"block text-sm font-medium text-gray-700\">\n                  Due Date\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  id=\"due_date\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.due_date}\n                  onChange={(e) => setNewTask({ ...newTask, due_date: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700\">\n                  Category\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"category\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTask.category}\n                  onChange={(e) => setNewTask({ ...newTask, category: e.target.value })}\n                  placeholder=\"e.g., Work, Personal, Health\"\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddForm(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Add Task\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Tasks List */}\n      <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n        {tasks.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <CheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No tasks</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">Get started by creating a new task.</p>\n          </div>\n        ) : (\n          <ul className=\"divide-y divide-gray-200\">\n            {tasks.map((task) => (\n              <li key={task.id} className=\"px-6 py-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <button\n                      onClick={() => toggleTask(task.id, task.completed)}\n                      className={`flex-shrink-0 h-5 w-5 rounded border-2 flex items-center justify-center ${\n                        task.completed\n                          ? 'bg-blue-600 border-blue-600'\n                          : 'border-gray-300 hover:border-blue-500'\n                      }`}\n                    >\n                      {task.completed && <CheckIcon className=\"h-3 w-3 text-white\" />}\n                    </button>\n                    <div className=\"ml-3\">\n                      <p className={`text-sm font-medium ${\n                        task.completed ? 'line-through text-gray-500' : 'text-gray-900'\n                      }`}>\n                        {task.title}\n                      </p>\n                      {task.description && (\n                        <p className=\"text-sm text-gray-500\">{task.description}</p>\n                      )}\n                      <div className=\"flex items-center mt-1 space-x-4\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>\n                          <FlagIcon className=\"h-3 w-3 mr-1\" />\n                          {task.priority}\n                        </span>\n                        {task.category && (\n                          <span className=\"text-xs text-gray-500\">{task.category}</span>\n                        )}\n                        {task.due_date && (\n                          <span className=\"inline-flex items-center text-xs text-gray-500\">\n                            <CalendarIcon className=\"h-3 w-3 mr-1\" />\n                            {new Date(task.due_date).toLocaleDateString()}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => deleteTask(task.id)}\n                    className=\"text-red-600 hover:text-red-900\"\n                  >\n                    <XIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </li>\n            ))}\n          </ul>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AASe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,SAAS,QAAQ,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU,OAAO;QACrB,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;gBACpD;oBACE,GAAG,OAAO;oBACV,SAAS,KAAK,EAAE;oBAChB,UAAU,QAAQ,QAAQ,IAAI;gBAChC;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,WAAW;gBACT,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,UAAU;YACZ;YACA,eAAe;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,aAAa,OAAO,QAAgB;QACxC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;gBAAE,WAAW,CAAC;YAAU,GAC/B,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,yMAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMxC,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,UAAU;;sCACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,QAAQ;4CACR,WAAU;4CACV,OAAO,QAAQ,KAAK;4CACpB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAGpE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAA0C;;;;;;sDAGjF,6LAAC;4CACC,IAAG;4CACH,MAAM;4CACN,WAAU;4CACV,OAAO,QAAQ,WAAW;4CAC1B,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAG1E,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAA8B;;8DAEhG,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,WAAU;4CACV,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAGvE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,WAAU;4CACV,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACnE,aAAY;;;;;;;;;;;;;;;;;;sCAIlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2MAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;yCAG5C,6LAAC;oBAAG,WAAU;8BACX,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;4BAAiB,WAAU;sCAC1B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,WAAW,KAAK,EAAE,EAAE,KAAK,SAAS;gDACjD,WAAW,CAAC,wEAAwE,EAClF,KAAK,SAAS,GACV,gCACA,yCACJ;0DAED,KAAK,SAAS,kBAAI,6LAAC,2MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAE1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAW,CAAC,oBAAoB,EACjC,KAAK,SAAS,GAAG,+BAA+B,iBAChD;kEACC,KAAK,KAAK;;;;;;oDAEZ,KAAK,WAAW,kBACf,6LAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;kEAExD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,iBAAiB,KAAK,QAAQ,GAAG;;kFAC3H,6LAAC,yMAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,KAAK,QAAQ;;;;;;;4DAEf,KAAK,QAAQ,kBACZ,6LAAC;gEAAK,WAAU;0EAAyB,KAAK,QAAQ;;;;;;4DAEvD,KAAK,QAAQ,kBACZ,6LAAC;gEAAK,WAAU;;kFACd,6LAAC,iNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEACvB,IAAI,KAAK,KAAK,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;kDAMrD,6LAAC;wCACC,SAAS,IAAM,WAAW,KAAK,EAAE;wCACjC,WAAU;kDAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BA3Cd,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAqD9B;GA5RwB;KAAA", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "file": "check.js", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/lucide-react/src/icons/check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]];\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('check', __iconNode);\n\nexport default Check;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,iBAAmB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAahF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "file": "calendar.js", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "file": "flag.js", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/lucide-react/src/icons/flag.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M4 22V4a1 1 0 0 1 .4-.8A6 6 0 0 1 8 2c3 0 5 2 7.333 2q2 0 3.067-.8A1 1 0 0 1 20 4v10a1 1 0 0 1-.4.8A6 6 0 0 1 16 16c-3 0-5-2-8-2a6 6 0 0 0-4 1.528',\n      key: '1jaruq',\n    },\n  ],\n];\n\n/**\n * @component @name Flag\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAyMlY0YTEgMSAwIDAgMSAuNC0uOEE2IDYgMCAwIDEgOCAyYzMgMCA1IDIgNy4zMzMgMnEyIDAgMy4wNjctLjhBMSAxIDAgMCAxIDIwIDR2MTBhMSAxIDAgMCAxLS40LjhBNiA2IDAgMCAxIDE2IDE2Yy0zIDAtNS0yLTgtMmE2IDYgMCAwIDAtNCAxLjUyOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/flag\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Flag = createLucideIcon('flag', __iconNode);\n\nexport default Flag;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}