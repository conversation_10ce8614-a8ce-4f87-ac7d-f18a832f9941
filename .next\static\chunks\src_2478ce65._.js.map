{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/shopping/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { PlusIcon, ShoppingCartIcon, CheckIcon, XIcon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\nimport Link from 'next/link'\n\ntype ShoppingList = Database['public']['Tables']['shopping_lists']['Row']\n\nexport default function ShoppingPage() {\n  const [shoppingLists, setShoppingLists] = useState<ShoppingList[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [newList, setNewList] = useState({\n    name: '',\n    description: '',\n  })\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchShoppingLists()\n  }, [])\n\n  const fetchShoppingLists = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('shopping_lists')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setShoppingLists(data || [])\n    } catch (error) {\n      console.error('Error fetching shopping lists:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addShoppingList = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('shopping_lists').insert([\n        {\n          ...newList,\n          user_id: user.id,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewList({\n        name: '',\n        description: '',\n      })\n      setShowAddForm(false)\n      fetchShoppingLists()\n    } catch (error) {\n      console.error('Error adding shopping list:', error)\n    }\n  }\n\n  const deleteShoppingList = async (listId: string) => {\n    try {\n      const { error } = await supabase\n        .from('shopping_lists')\n        .delete()\n        .eq('id', listId)\n\n      if (error) throw error\n      fetchShoppingLists()\n    } catch (error) {\n      console.error('Error deleting shopping list:', error)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div>\n      <div className=\"mb-8 flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Shopping Lists</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Organize your shopping with smart lists\n          </p>\n        </div>\n        <button\n          onClick={() => setShowAddForm(true)}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n        >\n          <PlusIcon className=\"h-4 w-4 mr-2\" />\n          New List\n        </button>\n      </div>\n\n      {/* Add Shopping List Form */}\n      {showAddForm && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <form onSubmit={addShoppingList}>\n            <div className=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                  List Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newList.name}\n                  onChange={(e) => setNewList({ ...newList, name: e.target.value })}\n                  placeholder=\"e.g., Weekly Groceries, Party Supplies\"\n                />\n              </div>\n              <div>\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n                  Description (Optional)\n                </label>\n                <textarea\n                  id=\"description\"\n                  rows={3}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newList.description}\n                  onChange={(e) => setNewList({ ...newList, description: e.target.value })}\n                  placeholder=\"Add any notes about this shopping list...\"\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddForm(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Create List\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Shopping Lists Grid */}\n      {shoppingLists.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <ShoppingCartIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No shopping lists</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">Get started by creating your first shopping list.</p>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n          {shoppingLists.map((list) => (\n            <div key={list.id} className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <ShoppingCartIcon className=\"h-6 w-6 text-blue-600\" />\n                    <h3 className=\"ml-2 text-lg font-medium text-gray-900 truncate\">\n                      {list.name}\n                    </h3>\n                  </div>\n                  <button\n                    onClick={() => deleteShoppingList(list.id)}\n                    className=\"text-red-600 hover:text-red-900\"\n                  >\n                    <XIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n                {list.description && (\n                  <p className=\"mt-2 text-sm text-gray-600\">{list.description}</p>\n                )}\n                <div className=\"mt-4\">\n                  <p className=\"text-xs text-gray-500\">\n                    Created {new Date(list.created_at).toLocaleDateString()}\n                  </p>\n                </div>\n                <div className=\"mt-4\">\n                  <Link\n                    href={`/shopping/${list.id}`}\n                    className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                  >\n                    View Items\n                  </Link>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;;;AANA;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM;QACN,aAAa;IACf;IACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,iBAAiB,QAAQ,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC;gBAC7D;oBACE,GAAG,OAAO;oBACV,SAAS,KAAK,EAAE;gBAClB;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,WAAW;gBACT,MAAM;gBACN,aAAa;YACf;YACA,eAAe;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,yMAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMxC,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,UAAU;;sCACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,QAAQ;4CACR,WAAU;4CACV,OAAO,QAAQ,IAAI;4CACnB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/D,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAA0C;;;;;;sDAGjF,6LAAC;4CACC,IAAG;4CACH,MAAM;4CACN,WAAU;4CACV,OAAO,QAAQ,WAAW;4CAC1B,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,aAAY;;;;;;;;;;;;;;;;;;sCAIlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,cAAc,MAAM,KAAK,kBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6NAAA,CAAA,mBAAgB;wBAAC,WAAU;;;;;;kCAC5B,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;qCAG5C,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wBAAkB,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6NAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;8DAC5B,6LAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;;;;;;;sDAGd,6LAAC;4CACC,SAAS,IAAM,mBAAmB,KAAK,EAAE;4CACzC,WAAU;sDAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAGpB,KAAK,WAAW,kBACf,6LAAC;oCAAE,WAAU;8CAA8B,KAAK,WAAW;;;;;;8CAE7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;4CAAwB;4CAC1B,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;8CAGzD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;wCAC5B,WAAU;kDACX;;;;;;;;;;;;;;;;;uBA5BG,KAAK,EAAE;;;;;;;;;;;;;;;;AAuC7B;GArMwB;KAAA", "debugId": null}}]}