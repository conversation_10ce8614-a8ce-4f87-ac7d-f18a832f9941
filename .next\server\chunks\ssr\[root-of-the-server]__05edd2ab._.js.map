{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { createClient } from '@/lib/supabase/server'\nimport { redirect } from 'next/navigation'\nimport {\n  CheckSquareIcon,\n  DollarSignIcon,\n  ShoppingCartIcon,\n  MessageCircleIcon,\n  ChefHatIcon,\n  ArrowRightIcon,\n  StarIcon\n} from 'lucide-react'\n\nexport default async function Home() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n\n  // If user is logged in, redirect to dashboard\n  if (user) {\n    redirect('/dashboard')\n  }\n\n  const features = [\n    {\n      name: 'Task Management',\n      description: 'Organize your tasks with priorities, due dates, and categories.',\n      icon: CheckSquareIcon,\n      color: 'text-blue-600',\n    },\n    {\n      name: 'Budget Tracking',\n      description: 'Monitor your income and expenses with detailed analytics.',\n      icon: DollarSignIcon,\n      color: 'text-green-600',\n    },\n    {\n      name: 'Shopping Lists',\n      description: 'Create and manage smart shopping lists with ease.',\n      icon: ShoppingCartIcon,\n      color: 'text-purple-600',\n    },\n    {\n      name: 'AI Assistant',\n      description: 'Get personalized help with life management decisions.',\n      icon: MessageCircleIcon,\n      color: 'text-indigo-600',\n    },\n    {\n      name: 'Recipe Manager',\n      description: 'Save recipes and import them automatically from URLs.',\n      icon: ChefHatIcon,\n      color: 'text-orange-600',\n    },\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">LifeManager</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/login\"\n                className=\"text-gray-600 hover:text-gray-900 font-medium\"\n              >\n                Sign In\n              </Link>\n              <Link\n                href=\"/signup\"\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 font-medium\"\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Manage Your Life\n            <span className=\"text-blue-600\"> Effortlessly</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            The all-in-one platform to organize your tasks, track your budget,\n            manage shopping lists, and get AI-powered assistance for better life management.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/signup\"\n              className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10\"\n            >\n              Start Free Today\n              <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n            </Link>\n            <Link\n              href=\"/login\"\n              className=\"inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10\"\n            >\n              Sign In\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Everything You Need in One Place\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Streamline your daily life with our comprehensive suite of management tools.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {features.map((feature) => (\n              <div key={feature.name} className=\"bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                <div className=\"flex items-center mb-4\">\n                  <feature.icon className={`h-8 w-8 ${feature.color}`} />\n                  <h3 className=\"text-xl font-semibold text-gray-900 ml-3\">\n                    {feature.name}\n                  </h3>\n                </div>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-blue-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n            Ready to Take Control?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Join thousands of users who have transformed their daily routines with LifeManager.\n          </p>\n          <Link\n            href=\"/signup\"\n            className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10\"\n          >\n            Get Started Free\n            <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n          </Link>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold mb-4\">LifeManager</h3>\n            <p className=\"text-gray-400 mb-4\">\n              Simplifying life management, one task at a time.\n            </p>\n            <p className=\"text-gray-500 text-sm\">\n              © 2024 LifeManager. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAUe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAEtD,8CAA8C;IAC9C,IAAI,MAAM;QACR,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,WAAW;QACf;YACE,MAAM;YACN,aAAa;YACb,MAAM,+NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,sNAAA,CAAA,iBAAc;YACpB,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,0NAAA,CAAA,mBAAgB;YACtB,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,4NAAA,CAAA,oBAAiB;YACvB,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,gNAAA,CAAA,cAAW;YACjB,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoD;8CAEhE,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,sNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;8CAE5B,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oCAAuB,WAAU;;sDAChC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,QAAQ,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;8DACnD,8OAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;;;;;;;sDAGjB,8OAAC;4CAAE,WAAU;sDAAiB,QAAQ,WAAW;;;;;;;mCAPzC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;0BAe9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC,sNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}]}