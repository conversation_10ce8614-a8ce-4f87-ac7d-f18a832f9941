import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { message, conversationId } = await request.json()

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Save user message
    const { error: userMessageError } = await supabase
      .from('chat_messages')
      .insert([
        {
          conversation_id: conversationId,
          role: 'user',
          content: message,
        },
      ])

    if (userMessageError) throw userMessageError

    // For now, we'll create a simple AI response
    // In a real implementation, you would integrate with OpenAI or another AI service
    const aiResponse = generateAIResponse(message)

    // Save AI response
    const { error: aiMessageError } = await supabase
      .from('chat_messages')
      .insert([
        {
          conversation_id: conversationId,
          role: 'assistant',
          content: aiResponse,
        },
      ])

    if (aiMessageError) throw aiMessageError

    return NextResponse.json({ response: aiResponse })
  } catch (error) {
    console.error('Error in chat API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateAIResponse(message: string): string {
  const lowerMessage = message.toLowerCase()
  
  if (lowerMessage.includes('task') || lowerMessage.includes('todo')) {
    return "I can help you manage your tasks! You can create, update, and organize your tasks in the Tasks section. Would you like me to help you create a new task or organize your existing ones?"
  }
  
  if (lowerMessage.includes('budget') || lowerMessage.includes('money') || lowerMessage.includes('expense')) {
    return "I can assist you with budget management! You can track your income and expenses in the Budget section. Would you like help setting up budget categories or analyzing your spending patterns?"
  }
  
  if (lowerMessage.includes('shopping') || lowerMessage.includes('grocery') || lowerMessage.includes('buy')) {
    return "I can help you organize your shopping! You can create shopping lists and manage items in the Shopping Lists section. Would you like me to help you create a new shopping list or suggest items based on your needs?"
  }
  
  if (lowerMessage.includes('recipe') || lowerMessage.includes('cook') || lowerMessage.includes('meal')) {
    return "I can help you with recipes and meal planning! You can save and organize recipes in the Recipes section. Would you like help finding a recipe or planning your meals for the week?"
  }
  
  if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
    return "Hello! I'm your LifeManager AI assistant. I can help you with tasks, budget management, shopping lists, and recipes. What would you like to work on today?"
  }
  
  return "I'm here to help you manage your life more effectively! I can assist with tasks, budgeting, shopping lists, and recipes. What specific area would you like help with?"
}
