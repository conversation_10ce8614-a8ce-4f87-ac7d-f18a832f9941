{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport { useRouter } from 'next/navigation'\nimport {\n  HomeIcon,\n  CheckSquareIcon,\n  DollarSignIcon,\n  ShoppingCartIcon,\n  MessageCircleIcon,\n  ChefHatIcon,\n  LogOutIcon,\n  MenuIcon,\n  XIcon,\n} from 'lucide-react'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Tasks', href: '/tasks', icon: CheckSquareIcon },\n  { name: 'Budget', href: '/budget', icon: DollarSignIcon },\n  { name: 'Shopping Lists', href: '/shopping', icon: ShoppingCartIcon },\n  { name: 'AI Chat', href: '/chat', icon: MessageCircleIcon },\n  { name: 'Recipes', href: '/recipes', icon: ChefHatIcon },\n]\n\nexport default function Sidebar() {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const router = useRouter()\n  const supabase = createClient()\n\n  const handleLogout = async () => {\n    await supabase.auth.signOut()\n    router.push('/login')\n    router.refresh()\n  }\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XIcon className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">LifeManager</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <item.icon className=\"mr-4 h-6 w-6\" />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </nav>\n          </div>\n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <button\n              onClick={handleLogout}\n              className=\"flex items-center text-gray-600 hover:text-gray-900\"\n            >\n              <LogOutIcon className=\"mr-3 h-5 w-5\" />\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">LifeManager</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </nav>\n          </div>\n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <button\n              onClick={handleLogout}\n              className=\"flex items-center text-gray-600 hover:text-gray-900\"\n            >\n              <LogOutIcon className=\"mr-3 h-5 w-5\" />\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu button */}\n      <div className=\"md:hidden\">\n        <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-4 py-2\">\n          <h1 className=\"text-lg font-semibold text-gray-900\">LifeManager</h1>\n          <button\n            type=\"button\"\n            className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <MenuIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,kOAAA,CAAA,kBAAe;IAAC;IACvD;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,yNAAA,CAAA,iBAAc;IAAC;IACxD;QAAE,MAAM;QAAkB,MAAM;QAAa,MAAM,6NAAA,CAAA,mBAAgB;IAAC;IACpE;QAAE,MAAM;QAAW,MAAM;QAAS,MAAM,+NAAA,CAAA,oBAAiB;IAAC;IAC1D;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mNAAA,CAAA,cAAW;IAAC;CACxD;AAEc,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,eAAe;QACnB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,OAAO,IAAI,CAAC;QACZ,OAAO,OAAO;IAChB;IAEA,qBACE;;0BAEE,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC;4CACf,MAAM,WAAW,aAAa,KAAK,IAAI;4CACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,mEAAmE,EAC7E,WACI,8BACA,sDACJ;;kEAEF,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;oDACpB,KAAK,IAAI;;+CATL,KAAK,IAAI;;;;;wCAYpB;;;;;;;;;;;;0CAGJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,iNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;8CAElD,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI;wCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,iEAAiE,EAC3E,WACI,8BACA,sDACJ;;8DAEF,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CATL,KAAK,IAAI;;;;;oCAYpB;;;;;;;;;;;;sCAGJ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;sCAE9B,cAAA,6LAAC,yMAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMhC;GArHwB;;QAEL,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}