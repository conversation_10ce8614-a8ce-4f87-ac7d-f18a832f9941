'use client'

import { useState } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Badge } from '@/components/ui/Badge'
import { 
  CalendarIcon, 
  FlagIcon, 
  TagIcon, 
  ClockIcon,
  PlusIcon,
  XIcon,
  SparklesIcon
} from 'lucide-react'
import { format } from 'date-fns'

const taskSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  priority: z.enum(['low', 'medium', 'high']),
  category: z.string().optional(),
  due_date: z.string().optional(),
  estimated_duration: z.number().min(1, 'Duration must be at least 1 minute').max(480, 'Duration cannot exceed 8 hours').optional(),
  tags: z.array(z.string()).optional(),
  subtasks: z.array(z.object({
    title: z.string().min(1, 'Subtask title is required'),
    completed: z.boolean().default(false)
  })).optional(),
})

type TaskFormData = z.infer<typeof taskSchema>

interface AdvancedTaskFormProps {
  onSubmit: (data: TaskFormData) => Promise<void>
  onCancel: () => void
  initialData?: Partial<TaskFormData>
  isLoading?: boolean
}

const priorityOptions = [
  { value: 'low', label: 'Low Priority', color: 'bg-green-100 text-green-800', icon: '🟢' },
  { value: 'medium', label: 'Medium Priority', color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },
  { value: 'high', label: 'High Priority', color: 'bg-red-100 text-red-800', icon: '🔴' },
]

const categoryOptions = [
  'Work', 'Personal', 'Health', 'Finance', 'Learning', 'Home', 'Travel', 'Shopping'
]

export default function AdvancedTaskForm({ 
  onSubmit, 
  onCancel, 
  initialData, 
  isLoading = false 
}: AdvancedTaskFormProps) {
  const [currentTag, setCurrentTag] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors, isValid }
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      priority: 'medium',
      tags: [],
      subtasks: [],
      ...initialData
    }
  })

  const watchedTags = watch('tags') || []
  const watchedSubtasks = watch('subtasks') || []
  const watchedPriority = watch('priority')

  const addTag = () => {
    if (currentTag.trim() && !watchedTags.includes(currentTag.trim())) {
      setValue('tags', [...watchedTags, currentTag.trim()])
      setCurrentTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove))
  }

  const addSubtask = () => {
    setValue('subtasks', [...watchedSubtasks, { title: '', completed: false }])
  }

  const removeSubtask = (index: number) => {
    setValue('subtasks', watchedSubtasks.filter((_, i) => i !== index))
  }

  const updateSubtask = (index: number, title: string) => {
    const newSubtasks = [...watchedSubtasks]
    newSubtasks[index] = { ...newSubtasks[index], title }
    setValue('subtasks', newSubtasks)
  }

  const selectedPriority = priorityOptions.find(p => p.value === watchedPriority)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <SparklesIcon className="h-5 w-5 text-blue-600" />
            <span>{initialData ? 'Edit Task' : 'Create New Task'}</span>
          </CardTitle>
          <CardDescription>
            {initialData ? 'Update your task details' : 'Add a new task to your list with advanced options'}
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div>
                <Input
                  label="Task Title"
                  placeholder="What needs to be done?"
                  error={!!errors.title}
                  helperText={errors.title?.message}
                  {...register('title')}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none"
                  rows={3}
                  placeholder="Add more details about this task..."
                  {...register('description')}
                />
                {errors.description && (
                  <p className="mt-1 text-xs text-red-600">{errors.description.message}</p>
                )}
              </div>

              {/* Priority Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority Level
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {priorityOptions.map((option) => (
                    <label key={option.value} className="cursor-pointer">
                      <input
                        type="radio"
                        value={option.value}
                        {...register('priority')}
                        className="sr-only"
                      />
                      <div className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                        watchedPriority === option.value 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}>
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{option.icon}</span>
                          <span className="text-sm font-medium">{option.label}</span>
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Advanced Options Toggle */}
            <div className="border-t pt-4">
              <button
                type="button"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
              >
                <span className="text-sm font-medium">
                  {showAdvanced ? 'Hide' : 'Show'} Advanced Options
                </span>
                <motion.div
                  animate={{ rotate: showAdvanced ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <ChevronDownIcon className="h-4 w-4" />
                </motion.div>
              </button>
            </div>

            <AnimatePresence>
              {showAdvanced && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4"
                >
                  {/* Category and Due Date */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Category
                      </label>
                      <select
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        {...register('category')}
                      >
                        <option value="">Select a category</option>
                        {categoryOptions.map((category) => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <Input
                        label="Due Date"
                        type="datetime-local"
                        {...register('due_date')}
                      />
                    </div>
                  </div>

                  {/* Estimated Duration */}
                  <div>
                    <Input
                      label="Estimated Duration (minutes)"
                      type="number"
                      placeholder="How long will this take?"
                      error={!!errors.estimated_duration}
                      helperText={errors.estimated_duration?.message}
                      {...register('estimated_duration', { valueAsNumber: true })}
                    />
                  </div>

                  {/* Tags */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tags
                    </label>
                    <div className="flex space-x-2 mb-2">
                      <Input
                        placeholder="Add a tag..."
                        value={currentTag}
                        onChange={(e) => setCurrentTag(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      />
                      <Button type="button" onClick={addTag} size="sm">
                        <PlusIcon className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {watchedTags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center space-x-1">
                          <TagIcon className="h-3 w-3" />
                          <span>{tag}</span>
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-1 hover:text-red-600"
                          >
                            <XIcon className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Subtasks */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-sm font-medium text-gray-700">
                        Subtasks
                      </label>
                      <Button type="button" onClick={addSubtask} size="sm" variant="outline">
                        <PlusIcon className="h-4 w-4 mr-1" />
                        Add Subtask
                      </Button>
                    </div>
                    <div className="space-y-2">
                      {watchedSubtasks.map((subtask, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Input
                            placeholder="Subtask title..."
                            value={subtask.title}
                            onChange={(e) => updateSubtask(index, e.target.value)}
                          />
                          <Button
                            type="button"
                            onClick={() => removeSubtask(index)}
                            size="sm"
                            variant="ghost"
                            className="text-red-600 hover:text-red-700"
                          >
                            <XIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                loading={isLoading}
                loadingText={initialData ? 'Updating...' : 'Creating...'}
                disabled={!isValid}
              >
                {initialData ? 'Update Task' : 'Create Task'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Missing ChevronDownIcon import - let me add it
function ChevronDownIcon({ className }: { className?: string }) {
  return (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
    </svg>
  )
}
