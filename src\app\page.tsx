'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { redirect } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import {
  CheckSquareIcon,
  DollarSignIcon,
  ShoppingCartIcon,
  MessageCircleIcon,
  ChefHatIcon,
  ArrowRightIcon,
  StarIcon,
  GlobeIcon,
  ShieldCheckIcon,
  ZapIcon,
  TrendingUpIcon,
  UsersIcon,
  CheckIcon,
  PlayIcon,
  SmartphoneIcon,
  LaptopIcon,
  TabletIcon,
  SparklesIcon,
  RocketIcon,
  HeartIcon,
  BrainIcon,
  ClockIcon,
  TargetIcon
} from 'lucide-react'

export default function Home() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        window.location.href = '/dashboard'
      }
      setUser(user)
      setLoading(false)
    }
    checkUser()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-white text-center"
        >
          <SparklesIcon className="h-16 w-16 mx-auto mb-4 animate-pulse" />
          <p className="text-xl">Loading LifeManager...</p>
        </motion.div>
      </div>
    )
  }

  const features = [
    {
      name: 'Smart Task Management',
      description: 'AI-powered task organization with drag-and-drop, priorities, and intelligent scheduling.',
      icon: CheckSquareIcon,
      color: 'from-blue-500 to-blue-600',
      stats: '10M+ tasks completed',
    },
    {
      name: 'Advanced Budget Tracking',
      description: 'Real-time expense monitoring with predictive analytics and automated categorization.',
      icon: DollarSignIcon,
      color: 'from-green-500 to-emerald-600',
      stats: '$2B+ tracked globally',
    },
    {
      name: 'Intelligent Shopping',
      description: 'Smart shopping lists with price tracking, store optimization, and family sharing.',
      icon: ShoppingCartIcon,
      color: 'from-purple-500 to-purple-600',
      stats: '500K+ lists created',
    },
    {
      name: 'AI Life Assistant',
      description: 'Personalized recommendations and insights powered by advanced machine learning.',
      icon: BrainIcon,
      color: 'from-indigo-500 to-indigo-600',
      stats: '1M+ decisions optimized',
    },
    {
      name: 'Recipe Intelligence',
      description: 'Auto-import recipes, nutritional analysis, and meal planning with dietary preferences.',
      icon: ChefHatIcon,
      color: 'from-orange-500 to-red-500',
      stats: '100K+ recipes saved',
    },
    {
      name: 'Global Sync & Security',
      description: 'End-to-end encryption with real-time sync across all your devices worldwide.',
      icon: ShieldCheckIcon,
      color: 'from-teal-500 to-cyan-600',
      stats: '99.9% uptime guarantee',
    },
  ]

  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'Product Manager',
      location: 'San Francisco, USA',
      image: '/api/placeholder/64/64',
      content: 'LifeManager transformed how I organize my life. The AI suggestions are incredibly accurate and have saved me hours every week.',
      rating: 5,
    },
    {
      name: 'Marcus Johnson',
      role: 'Entrepreneur',
      location: 'London, UK',
      image: '/api/placeholder/64/64',
      content: 'As someone juggling multiple businesses, LifeManager keeps me on track. The budget tracking alone has saved me thousands.',
      rating: 5,
    },
    {
      name: 'Yuki Tanaka',
      role: 'Software Engineer',
      location: 'Tokyo, Japan',
      image: '/api/placeholder/64/64',
      content: 'The cross-platform sync is flawless. I can start a task on my phone and finish it on my laptop seamlessly.',
      rating: 5,
    },
  ]

  const stats = [
    { label: 'Active Users Worldwide', value: '2.5M+', icon: UsersIcon },
    { label: 'Countries Supported', value: '150+', icon: GlobeIcon },
    { label: 'Tasks Completed Daily', value: '50K+', icon: TargetIcon },
    { label: 'Customer Satisfaction', value: '98%', icon: HeartIcon },
  ]

  const pricingPlans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      description: 'Perfect for getting started',
      features: [
        'Up to 50 tasks',
        'Basic budget tracking',
        '3 shopping lists',
        'Mobile & web access',
        'Community support'
      ],
      popular: false,
    },
    {
      name: 'Pro',
      price: '$9.99',
      period: 'per month',
      description: 'For power users and families',
      features: [
        'Unlimited tasks & projects',
        'Advanced analytics',
        'Unlimited shopping lists',
        'AI-powered insights',
        'Priority support',
        'Team collaboration',
        'Advanced integrations'
      ],
      popular: true,
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      period: 'contact us',
      description: 'For organizations and teams',
      features: [
        'Everything in Pro',
        'Custom integrations',
        'Advanced security',
        'Dedicated support',
        'Training & onboarding',
        'SLA guarantee'
      ],
      popular: false,
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">LifeManager</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/login"
                className="text-gray-600 hover:text-gray-900 font-medium"
              >
                Sign In
              </Link>
              <Link
                href="/signup"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 font-medium"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Manage Your Life
            <span className="text-blue-600"> Effortlessly</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            The all-in-one platform to organize your tasks, track your budget,
            manage shopping lists, and get AI-powered assistance for better life management.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/signup"
              className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10"
            >
              Start Free Today
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
            <Link
              href="/login"
              className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"
            >
              Sign In
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need in One Place
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Streamline your daily life with our comprehensive suite of management tools.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => (
              <div key={feature.name} className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-4">
                  <feature.icon className={`h-8 w-8 ${feature.color}`} />
                  <h3 className="text-xl font-semibold text-gray-900 ml-3">
                    {feature.name}
                  </h3>
                </div>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Take Control?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of users who have transformed their daily routines with LifeManager.
          </p>
          <Link
            href="/signup"
            className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"
          >
            Get Started Free
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">LifeManager</h3>
            <p className="text-gray-400 mb-4">
              Simplifying life management, one task at a time.
            </p>
            <p className="text-gray-500 text-sm">
              © 2024 LifeManager. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
