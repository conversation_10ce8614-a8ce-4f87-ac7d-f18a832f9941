{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n  helperText?: string\n  label?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, helperText, label, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\" aria-label=\"required\">*</span>}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 hover:border-gray-400',\n            error && 'border-red-500 focus-visible:ring-red-500 aria-invalid:border-red-500',\n            className\n          )}\n          ref={ref}\n          aria-invalid={error}\n          aria-describedby={helperTextId}\n          {...props}\n        />\n        {helperText && (\n          <p\n            id={helperTextId}\n            className={cn(\n              'mt-1 text-xs',\n              error ? 'text-red-600' : 'text-gray-500'\n            )}\n            role={error ? 'alert' : 'status'}\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,6LAAC;wBAAK,WAAU;wBAAoB,cAAW;kCAAW;;;;;;;;;;;;0BAGjF,6LAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA,SAAS,yEACT;gBAEF,KAAK;gBACL,gBAAc;gBACd,oBAAkB;gBACjB,GAAG,KAAK;;;;;;YAEV,4BACC,6LAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,QAAQ,iBAAiB;gBAE3B,MAAM,QAAQ,UAAU;0BAEvB;;;;;;;;;;;;AAKX;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse'\n  className?: string\n  text?: string\n}\n\nconst sizeClasses = {\n  sm: 'h-4 w-4',\n  md: 'h-6 w-6',\n  lg: 'h-8 w-8',\n  xl: 'h-12 w-12',\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce\"></div>\n        </div>\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  return null\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('animate-pulse', className)}>\n      <div className=\"bg-gray-200 rounded-lg h-4 w-full\"></div>\n    </div>\n  )\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-6 animate-pulse\">\n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n        </div>\n        <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingTable({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {Array.from({ length: rows }).map((_, i) => (\n          <div key={i} className=\"p-4 animate-pulse\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"h-4 w-4 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingStats() {\n  return (\n    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-4 animate-pulse\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-gray-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mx-auto\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AASA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS;IACb,IAAI,YAAY,WAAW;QACzB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;gBAEhB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,OAAO;AACT;KAhDgB;AAkDT,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MANgB;AAQT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;MAbgB;AAeT,SAAS,aAAa,EAAE,OAAO,CAAC,EAAqB;IAC1D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;;;;;;;;AAcpB;MAtBgB;AAwBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gBAAY,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;eAHT;;;;;;;;;;AASlB;MAbgB", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode, useEffect } from 'react'\nimport { X } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title?: string\n  description?: string\n  children: ReactNode\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'\n  className?: string\n}\n\nconst sizeClasses = {\n  sm: 'max-w-md',\n  md: 'max-w-lg',\n  lg: 'max-w-2xl',\n  xl: 'max-w-4xl',\n  full: 'max-w-7xl',\n}\n\nexport function Modal({\n  isOpen,\n  onClose,\n  title,\n  description,\n  children,\n  size = 'md',\n  className,\n}: ModalProps) {\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose()\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'hidden'\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, onClose])\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        {/* Backdrop */}\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm transition-opacity duration-300\"\n          onClick={onClose}\n        />\n\n        {/* Modal */}\n        <div\n          className={cn(\n            'relative w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left shadow-xl transition-all duration-300 animate-slide-up',\n            sizeClasses[size],\n            className\n          )}\n          onClick={(e) => e.stopPropagation()}\n        >\n          {(title || description) && (\n            <div className=\"mb-6\">\n              <div className=\"flex items-center justify-between\">\n                {title && (\n                  <h3 className=\"text-lg font-semibold leading-6 text-gray-900\">\n                    {title}\n                  </h3>\n                )}\n                <button\n                  type=\"button\"\n                  className=\"rounded-md p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\"\n                  onClick={onClose}\n                >\n                  <X className=\"h-5 w-5\" />\n                </button>\n              </div>\n              {description && (\n                <div className=\"mt-2\">\n                  <p className=\"text-sm text-gray-600\">{description}</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          <div>{children}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface ConfirmModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  variant?: 'danger' | 'warning' | 'info'\n}\n\nexport function ConfirmModal({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  variant = 'info',\n}: ConfirmModalProps) {\n  const handleConfirm = () => {\n    onConfirm()\n    onClose()\n  }\n\n  const buttonVariant = variant === 'danger' ? 'destructive' : 'default'\n\n  return (\n    <Modal isOpen={isOpen} onClose={onClose} size=\"sm\">\n      <div className=\"text-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n        <p className=\"text-gray-600 mb-6\">{message}</p>\n        <div className=\"flex justify-center space-x-3\">\n          <button\n            type=\"button\"\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\n            onClick={onClose}\n          >\n            {cancelText}\n          </button>\n          <button\n            type=\"button\"\n            className={cn(\n              'px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors',\n              variant === 'danger'\n                ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'\n                : variant === 'warning'\n                ? 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'\n                : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'\n            )}\n            onClick={handleConfirm}\n          >\n            {confirmText}\n          </button>\n        </div>\n      </div>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAgBA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;AACR;AAEO,SAAS,MAAM,EACpB,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,IAAI,EACX,SAAS,EACE;;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uIACA,WAAW,CAAC,KAAK,EACjB;oBAEF,SAAS,CAAC,IAAM,EAAE,eAAe;;wBAEhC,CAAC,SAAS,WAAW,mBACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,uBACC,6LAAC;4CAAG,WAAU;sDACX;;;;;;sDAGL,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;sDAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAGhB,6BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;sCAM9C,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GA5EgB;KAAA;AAyFT,SAAS,aAAa,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,UAAU,MAAM,EACE;IAClB,MAAM,gBAAgB;QACpB;QACA;IACF;IAEA,MAAM,gBAAgB,YAAY,WAAW,gBAAgB;IAE7D,qBACE,6LAAC;QAAM,QAAQ;QAAQ,SAAS;QAAS,MAAK;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,6LAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;sCAER;;;;;;sCAEH,6LAAC;4BACC,MAAK;4BACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6HACA,YAAY,WACR,mDACA,YAAY,YACZ,4DACA;4BAEN,SAAS;sCAER;;;;;;;;;;;;;;;;;;;;;;;AAMb;MAhDgB", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/tasks/DragDropTaskBoard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  DndContext,\n  DragEndEvent,\n  DragOverlay,\n  DragStartEvent,\n  PointerSensor,\n  useSensor,\n  useSensors,\n} from '@dnd-kit/core'\nimport {\n  SortableContext,\n  arrayMove,\n  verticalListSortingStrategy,\n} from '@dnd-kit/sortable'\nimport {\n  useSortable,\n} from '@dnd-kit/sortable'\nimport { CSS } from '@dnd-kit/utilities'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Badge } from '@/components/ui/Badge'\nimport { Button } from '@/components/ui/Button'\nimport { \n  CalendarIcon, \n  FlagIcon, \n  GripVerticalIcon,\n  CheckIcon,\n  XIcon,\n  ClockIcon\n} from 'lucide-react'\nimport { Database } from '@/lib/types/database'\nimport { motion, AnimatePresence } from 'framer-motion'\n\ntype Task = Database['public']['Tables']['tasks']['Row']\n\ninterface TaskCardProps {\n  task: Task\n  onToggle: (taskId: string, completed: boolean) => void\n  onDelete: (taskId: string) => void\n}\n\nfunction TaskCard({ task, onToggle, onDelete }: TaskCardProps) {\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging,\n  } = useSortable({ id: task.id })\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n  }\n\n  const getPriorityVariant = (priority: string) => {\n    switch (priority) {\n      case 'high': return 'destructive'\n      case 'medium': return 'warning'\n      case 'low': return 'success'\n      default: return 'secondary'\n    }\n  }\n\n  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && !task.completed\n\n  return (\n    <motion.div\n      ref={setNodeRef}\n      style={style}\n      {...attributes}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      className={`group relative ${isDragging ? 'opacity-50' : ''}`}\n    >\n      <Card className={`transition-all duration-200 hover:shadow-md ${\n        task.completed ? 'bg-gray-50 border-gray-200' : 'bg-white hover:border-blue-300'\n      } ${isOverdue ? 'border-l-4 border-l-red-500' : ''}`}>\n        <CardContent className=\"p-4\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-3 flex-1\">\n              <button\n                onClick={() => onToggle(task.id, task.completed)}\n                className={`flex-shrink-0 mt-1 h-5 w-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${\n                  task.completed\n                    ? 'bg-green-600 border-green-600 hover:bg-green-700'\n                    : 'border-gray-300 hover:border-blue-500 hover:bg-blue-50'\n                }`}\n              >\n                {task.completed && <CheckIcon className=\"h-3 w-3 text-white\" />}\n              </button>\n              \n              <div className=\"flex-1 min-w-0\">\n                <h3 className={`text-sm font-medium transition-all duration-200 ${\n                  task.completed ? 'line-through text-gray-500' : 'text-gray-900'\n                }`}>\n                  {task.title}\n                </h3>\n                \n                {task.description && (\n                  <p className={`text-sm mt-1 ${\n                    task.completed ? 'text-gray-400' : 'text-gray-600'\n                  }`}>\n                    {task.description}\n                  </p>\n                )}\n                \n                <div className=\"flex items-center mt-2 space-x-2 flex-wrap gap-1\">\n                  <Badge variant={getPriorityVariant(task.priority)} size=\"sm\">\n                    <FlagIcon className=\"h-3 w-3 mr-1\" />\n                    {task.priority}\n                  </Badge>\n                  \n                  {task.category && (\n                    <Badge variant=\"outline\" size=\"sm\">\n                      {task.category}\n                    </Badge>\n                  )}\n                  \n                  {task.due_date && (\n                    <Badge \n                      variant={isOverdue ? 'destructive' : 'secondary'} \n                      size=\"sm\"\n                    >\n                      <CalendarIcon className=\"h-3 w-3 mr-1\" />\n                      {new Date(task.due_date).toLocaleDateString()}\n                    </Badge>\n                  )}\n                  \n                  {isOverdue && (\n                    <Badge variant=\"destructive\" size=\"sm\">\n                      <ClockIcon className=\"h-3 w-3 mr-1\" />\n                      Overdue\n                    </Badge>\n                  )}\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n              <button\n                {...listeners}\n                className=\"p-1 text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing\"\n              >\n                <GripVerticalIcon className=\"h-4 w-4\" />\n              </button>\n              \n              <Button\n                onClick={() => onDelete(task.id)}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-red-600 hover:text-red-700 hover:bg-red-50 h-8 w-8 p-0\"\n              >\n                <XIcon className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n\ninterface DragDropTaskBoardProps {\n  tasks: Task[]\n  onTasksReorder: (tasks: Task[]) => void\n  onToggleTask: (taskId: string, completed: boolean) => void\n  onDeleteTask: (taskId: string) => void\n}\n\nexport default function DragDropTaskBoard({ \n  tasks, \n  onTasksReorder, \n  onToggleTask, \n  onDeleteTask \n}: DragDropTaskBoardProps) {\n  const [activeId, setActiveId] = useState<string | null>(null)\n  const sensors = useSensors(useSensor(PointerSensor))\n\n  const handleDragStart = (event: DragStartEvent) => {\n    setActiveId(event.active.id as string)\n  }\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    const { active, over } = event\n\n    if (active.id !== over?.id) {\n      const oldIndex = tasks.findIndex((task) => task.id === active.id)\n      const newIndex = tasks.findIndex((task) => task.id === over?.id)\n      \n      const newTasks = arrayMove(tasks, oldIndex, newIndex)\n      onTasksReorder(newTasks)\n    }\n\n    setActiveId(null)\n  }\n\n  const activeTask = activeId ? tasks.find(task => task.id === activeId) : null\n\n  // Separate completed and pending tasks\n  const pendingTasks = tasks.filter(task => !task.completed)\n  const completedTasks = tasks.filter(task => task.completed)\n\n  return (\n    <DndContext\n      sensors={sensors}\n      onDragStart={handleDragStart}\n      onDragEnd={handleDragEnd}\n    >\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Pending Tasks */}\n        <div>\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <div className=\"h-3 w-3 bg-blue-500 rounded-full\"></div>\n                <span>Pending Tasks ({pendingTasks.length})</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <SortableContext \n                items={pendingTasks.map(task => task.id)} \n                strategy={verticalListSortingStrategy}\n              >\n                <AnimatePresence>\n                  <div className=\"space-y-3\">\n                    {pendingTasks.map((task) => (\n                      <TaskCard\n                        key={task.id}\n                        task={task}\n                        onToggle={onToggleTask}\n                        onDelete={onDeleteTask}\n                      />\n                    ))}\n                    {pendingTasks.length === 0 && (\n                      <motion.div \n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        className=\"text-center py-8 text-gray-500\"\n                      >\n                        No pending tasks. Great job! 🎉\n                      </motion.div>\n                    )}\n                  </div>\n                </AnimatePresence>\n              </SortableContext>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Completed Tasks */}\n        <div>\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <div className=\"h-3 w-3 bg-green-500 rounded-full\"></div>\n                <span>Completed Tasks ({completedTasks.length})</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <AnimatePresence>\n                  {completedTasks.map((task) => (\n                    <TaskCard\n                      key={task.id}\n                      task={task}\n                      onToggle={onToggleTask}\n                      onDelete={onDeleteTask}\n                    />\n                  ))}\n                  {completedTasks.length === 0 && (\n                    <motion.div \n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"text-center py-8 text-gray-500\"\n                    >\n                      No completed tasks yet.\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      <DragOverlay>\n        {activeTask ? (\n          <TaskCard\n            task={activeTask}\n            onToggle={() => {}}\n            onDelete={() => {}}\n          />\n        ) : null}\n      </DragOverlay>\n    </DndContext>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;;;AAjCA;;;;;;;;;;;AA2CA,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAiB;;IAC3D,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,KAAK,EAAE;IAAC;IAE9B,MAAM,QAAQ;QACZ,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,YAAY,KAAK,QAAQ,IAAI,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,UAAU,CAAC,KAAK,SAAS;IAE1F,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,OAAO;QACN,GAAG,UAAU;QACd,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,WAAW,CAAC,eAAe,EAAE,aAAa,eAAe,IAAI;kBAE7D,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW,CAAC,4CAA4C,EAC5D,KAAK,SAAS,GAAG,+BAA+B,iCACjD,CAAC,EAAE,YAAY,gCAAgC,IAAI;sBAClD,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,SAAS,KAAK,EAAE,EAAE,KAAK,SAAS;oCAC/C,WAAW,CAAC,yGAAyG,EACnH,KAAK,SAAS,GACV,qDACA,0DACJ;8CAED,KAAK,SAAS,kBAAI,6LAAC,2MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAG1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAC,gDAAgD,EAC9D,KAAK,SAAS,GAAG,+BAA+B,iBAChD;sDACC,KAAK,KAAK;;;;;;wCAGZ,KAAK,WAAW,kBACf,6LAAC;4CAAE,WAAW,CAAC,aAAa,EAC1B,KAAK,SAAS,GAAG,kBAAkB,iBACnC;sDACC,KAAK,WAAW;;;;;;sDAIrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAS,mBAAmB,KAAK,QAAQ;oDAAG,MAAK;;sEACtD,6LAAC,yMAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,KAAK,QAAQ;;;;;;;gDAGf,KAAK,QAAQ,kBACZ,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,MAAK;8DAC3B,KAAK,QAAQ;;;;;;gDAIjB,KAAK,QAAQ,kBACZ,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAS,YAAY,gBAAgB;oDACrC,MAAK;;sEAEL,6LAAC,iNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDACvB,IAAI,KAAK,KAAK,QAAQ,EAAE,kBAAkB;;;;;;;gDAI9C,2BACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAc,MAAK;;sEAChC,6LAAC,2MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACE,GAAG,SAAS;oCACb,WAAU;8CAEV,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;;;;;;8CAG9B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,SAAS,KAAK,EAAE;oCAC/B,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC;GA1HS;;QAQH,sKAAA,CAAA,cAAW;;;KARR;AAmIM,SAAS,kBAAkB,EACxC,KAAK,EACL,cAAc,EACd,YAAY,EACZ,YAAY,EACW;;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa;IAElD,MAAM,kBAAkB,CAAC;QACvB,YAAY,MAAM,MAAM,CAAC,EAAE;IAC7B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,OAAO,EAAE,KAAK,MAAM,IAAI;YAC1B,MAAM,WAAW,MAAM,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK,OAAO,EAAE;YAChE,MAAM,WAAW,MAAM,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK,MAAM;YAE7D,MAAM,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,OAAO,UAAU;YAC5C,eAAe;QACjB;QAEA,YAAY;IACd;IAEA,MAAM,aAAa,WAAW,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY;IAEzE,uCAAuC;IACvC,MAAM,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS;IACzD,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;IAE1D,qBACE,6LAAC,8JAAA,CAAA,aAAU;QACT,SAAS;QACT,aAAa;QACb,WAAW;;0BAEX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;kCACC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;;oDAAK;oDAAgB,aAAa,MAAM;oDAAC;;;;;;;;;;;;;;;;;;8CAG9C,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,sKAAA,CAAA,kBAAe;wCACd,OAAO,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;wCACvC,UAAU,sKAAA,CAAA,8BAA2B;kDAErC,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC;4DAEC,MAAM;4DACN,UAAU;4DACV,UAAU;2DAHL,KAAK,EAAE;;;;;oDAMf,aAAa,MAAM,KAAK,mBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,SAAS;wDAAE;wDACtB,SAAS;4DAAE,SAAS;wDAAE;wDACtB,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;kCACC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;;oDAAK;oDAAkB,eAAe,MAAM;oDAAC;;;;;;;;;;;;;;;;;;8CAGlD,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;;gDACb,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC;wDAEC,MAAM;wDACN,UAAU;wDACV,UAAU;uDAHL,KAAK,EAAE;;;;;gDAMf,eAAe,MAAM,KAAK,mBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;oDAAE;oDACtB,SAAS;wDAAE,SAAS;oDAAE;oDACtB,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWf,6LAAC,8JAAA,CAAA,cAAW;0BACT,2BACC,6LAAC;oBACC,MAAM;oBACN,UAAU,KAAO;oBACjB,UAAU,KAAO;;;;;2BAEjB;;;;;;;;;;;;AAIZ;IA/HwB;;QAON,8JAAA,CAAA,aAAU;;;MAPJ", "debugId": null}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/forms/AdvancedTaskForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { use<PERSON><PERSON>, Controller } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Badge } from '@/components/ui/Badge'\nimport { \n  CalendarIcon, \n  FlagIcon, \n  TagIcon, \n  ClockIcon,\n  PlusIcon,\n  XIcon,\n  SparklesIcon\n} from 'lucide-react'\nimport { format } from 'date-fns'\n\nconst taskSchema = z.object({\n  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),\n  description: z.string().max(500, 'Description must be less than 500 characters').optional(),\n  priority: z.enum(['low', 'medium', 'high']),\n  category: z.string().optional(),\n  due_date: z.string().optional(),\n  estimated_duration: z.number().min(1, 'Duration must be at least 1 minute').max(480, 'Duration cannot exceed 8 hours').optional(),\n  tags: z.array(z.string()).optional(),\n  subtasks: z.array(z.object({\n    title: z.string().min(1, 'Subtask title is required'),\n    completed: z.boolean().default(false)\n  })).optional(),\n})\n\ntype TaskFormData = z.infer<typeof taskSchema>\n\ninterface AdvancedTaskFormProps {\n  onSubmit: (data: TaskFormData) => Promise<void>\n  onCancel: () => void\n  initialData?: Partial<TaskFormData>\n  isLoading?: boolean\n}\n\nconst priorityOptions = [\n  { value: 'low', label: 'Low Priority', color: 'bg-green-100 text-green-800', icon: '🟢' },\n  { value: 'medium', label: 'Medium Priority', color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },\n  { value: 'high', label: 'High Priority', color: 'bg-red-100 text-red-800', icon: '🔴' },\n]\n\nconst categoryOptions = [\n  'Work', 'Personal', 'Health', 'Finance', 'Learning', 'Home', 'Travel', 'Shopping'\n]\n\nexport default function AdvancedTaskForm({ \n  onSubmit, \n  onCancel, \n  initialData, \n  isLoading = false \n}: AdvancedTaskFormProps) {\n  const [currentTag, setCurrentTag] = useState('')\n  const [showAdvanced, setShowAdvanced] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    control,\n    watch,\n    setValue,\n    formState: { errors, isValid }\n  } = useForm<TaskFormData>({\n    resolver: zodResolver(taskSchema),\n    defaultValues: {\n      priority: 'medium',\n      tags: [],\n      subtasks: [],\n      ...initialData\n    }\n  })\n\n  const watchedTags = watch('tags') || []\n  const watchedSubtasks = watch('subtasks') || []\n  const watchedPriority = watch('priority')\n\n  const addTag = () => {\n    if (currentTag.trim() && !watchedTags.includes(currentTag.trim())) {\n      setValue('tags', [...watchedTags, currentTag.trim()])\n      setCurrentTag('')\n    }\n  }\n\n  const removeTag = (tagToRemove: string) => {\n    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove))\n  }\n\n  const addSubtask = () => {\n    setValue('subtasks', [...watchedSubtasks, { title: '', completed: false }])\n  }\n\n  const removeSubtask = (index: number) => {\n    setValue('subtasks', watchedSubtasks.filter((_, i) => i !== index))\n  }\n\n  const updateSubtask = (index: number, title: string) => {\n    const newSubtasks = [...watchedSubtasks]\n    newSubtasks[index] = { ...newSubtasks[index], title }\n    setValue('subtasks', newSubtasks)\n  }\n\n  const selectedPriority = priorityOptions.find(p => p.value === watchedPriority)\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n    >\n      <Card className=\"w-full max-w-2xl mx-auto\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <SparklesIcon className=\"h-5 w-5 text-blue-600\" />\n            <span>{initialData ? 'Edit Task' : 'Create New Task'}</span>\n          </CardTitle>\n          <CardDescription>\n            {initialData ? 'Update your task details' : 'Add a new task to your list with advanced options'}\n          </CardDescription>\n        </CardHeader>\n        \n        <CardContent>\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"space-y-4\">\n              <div>\n                <Input\n                  label=\"Task Title\"\n                  placeholder=\"What needs to be done?\"\n                  error={!!errors.title}\n                  helperText={errors.title?.message}\n                  {...register('title')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none\"\n                  rows={3}\n                  placeholder=\"Add more details about this task...\"\n                  {...register('description')}\n                />\n                {errors.description && (\n                  <p className=\"mt-1 text-xs text-red-600\">{errors.description.message}</p>\n                )}\n              </div>\n\n              {/* Priority Selection */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Priority Level\n                </label>\n                <div className=\"grid grid-cols-3 gap-2\">\n                  {priorityOptions.map((option) => (\n                    <label key={option.value} className=\"cursor-pointer\">\n                      <input\n                        type=\"radio\"\n                        value={option.value}\n                        {...register('priority')}\n                        className=\"sr-only\"\n                      />\n                      <div className={`p-3 rounded-lg border-2 transition-all duration-200 ${\n                        watchedPriority === option.value \n                          ? 'border-blue-500 bg-blue-50' \n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}>\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-lg\">{option.icon}</span>\n                          <span className=\"text-sm font-medium\">{option.label}</span>\n                        </div>\n                      </div>\n                    </label>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Advanced Options Toggle */}\n            <div className=\"border-t pt-4\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAdvanced(!showAdvanced)}\n                className=\"flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors\"\n              >\n                <span className=\"text-sm font-medium\">\n                  {showAdvanced ? 'Hide' : 'Show'} Advanced Options\n                </span>\n                <motion.div\n                  animate={{ rotate: showAdvanced ? 180 : 0 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <ChevronDownIcon className=\"h-4 w-4\" />\n                </motion.div>\n              </button>\n            </div>\n\n            <AnimatePresence>\n              {showAdvanced && (\n                <motion.div\n                  initial={{ opacity: 0, height: 0 }}\n                  animate={{ opacity: 1, height: 'auto' }}\n                  exit={{ opacity: 0, height: 0 }}\n                  className=\"space-y-4\"\n                >\n                  {/* Category and Due Date */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Category\n                      </label>\n                      <select\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                        {...register('category')}\n                      >\n                        <option value=\"\">Select a category</option>\n                        {categoryOptions.map((category) => (\n                          <option key={category} value={category}>\n                            {category}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div>\n                      <Input\n                        label=\"Due Date\"\n                        type=\"datetime-local\"\n                        {...register('due_date')}\n                      />\n                    </div>\n                  </div>\n\n                  {/* Estimated Duration */}\n                  <div>\n                    <Input\n                      label=\"Estimated Duration (minutes)\"\n                      type=\"number\"\n                      placeholder=\"How long will this take?\"\n                      error={!!errors.estimated_duration}\n                      helperText={errors.estimated_duration?.message}\n                      {...register('estimated_duration', { valueAsNumber: true })}\n                    />\n                  </div>\n\n                  {/* Tags */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Tags\n                    </label>\n                    <div className=\"flex space-x-2 mb-2\">\n                      <Input\n                        placeholder=\"Add a tag...\"\n                        value={currentTag}\n                        onChange={(e) => setCurrentTag(e.target.value)}\n                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}\n                      />\n                      <Button type=\"button\" onClick={addTag} size=\"sm\">\n                        <PlusIcon className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {watchedTags.map((tag) => (\n                        <Badge key={tag} variant=\"secondary\" className=\"flex items-center space-x-1\">\n                          <TagIcon className=\"h-3 w-3\" />\n                          <span>{tag}</span>\n                          <button\n                            type=\"button\"\n                            onClick={() => removeTag(tag)}\n                            className=\"ml-1 hover:text-red-600\"\n                          >\n                            <XIcon className=\"h-3 w-3\" />\n                          </button>\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Subtasks */}\n                  <div>\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <label className=\"block text-sm font-medium text-gray-700\">\n                        Subtasks\n                      </label>\n                      <Button type=\"button\" onClick={addSubtask} size=\"sm\" variant=\"outline\">\n                        <PlusIcon className=\"h-4 w-4 mr-1\" />\n                        Add Subtask\n                      </Button>\n                    </div>\n                    <div className=\"space-y-2\">\n                      {watchedSubtasks.map((subtask, index) => (\n                        <div key={index} className=\"flex items-center space-x-2\">\n                          <Input\n                            placeholder=\"Subtask title...\"\n                            value={subtask.title}\n                            onChange={(e) => updateSubtask(index, e.target.value)}\n                          />\n                          <Button\n                            type=\"button\"\n                            onClick={() => removeSubtask(index)}\n                            size=\"sm\"\n                            variant=\"ghost\"\n                            className=\"text-red-600 hover:text-red-700\"\n                          >\n                            <XIcon className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n\n            {/* Form Actions */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t\">\n              <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n                Cancel\n              </Button>\n              <Button \n                type=\"submit\" \n                loading={isLoading}\n                loadingText={initialData ? 'Updating...' : 'Creating...'}\n                disabled={!isValid}\n              >\n                {initialData ? 'Update Task' : 'Create Task'}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n\n// Missing ChevronDownIcon import - let me add it\nfunction ChevronDownIcon({ className }: { className?: string }) {\n  return (\n    <svg className={className} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n    </svg>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAXA;;;;;;;;;;;AAsBA,MAAM,aAAa,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,KAAK;IACvD,aAAa,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gDAAgD,QAAQ;IACzF,UAAU,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAU;KAAO;IAC1C,UAAU,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,UAAU,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,oBAAoB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,sCAAsC,GAAG,CAAC,KAAK,kCAAkC,QAAQ;IAC/H,MAAM,qKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,qKAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IAClC,UAAU,qKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACzB,WAAW,qKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACjC,IAAI,QAAQ;AACd;AAWA,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAO,OAAO;QAAgB,OAAO;QAA+B,MAAM;IAAK;IACxF;QAAE,OAAO;QAAU,OAAO;QAAmB,OAAO;QAAiC,MAAM;IAAK;IAChG;QAAE,OAAO;QAAQ,OAAO;QAAiB,OAAO;QAA2B,MAAM;IAAK;CACvF;AAED,MAAM,kBAAkB;IACtB;IAAQ;IAAY;IAAU;IAAW;IAAY;IAAQ;IAAU;CACxE;AAEc,SAAS,iBAAiB,EACvC,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,YAAY,KAAK,EACK;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAgB;QACxB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,UAAU;YACV,MAAM,EAAE;YACR,UAAU,EAAE;YACZ,GAAG,WAAW;QAChB;IACF;IAEA,MAAM,cAAc,MAAM,WAAW,EAAE;IACvC,MAAM,kBAAkB,MAAM,eAAe,EAAE;IAC/C,MAAM,kBAAkB,MAAM;IAE9B,MAAM,SAAS;QACb,IAAI,WAAW,IAAI,MAAM,CAAC,YAAY,QAAQ,CAAC,WAAW,IAAI,KAAK;YACjE,SAAS,QAAQ;mBAAI;gBAAa,WAAW,IAAI;aAAG;YACpD,cAAc;QAChB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,SAAS,QAAQ,YAAY,MAAM,CAAC,CAAA,MAAO,QAAQ;IACrD;IAEA,MAAM,aAAa;QACjB,SAAS,YAAY;eAAI;YAAiB;gBAAE,OAAO;gBAAI,WAAW;YAAM;SAAE;IAC5E;IAEA,MAAM,gBAAgB,CAAC;QACrB,SAAS,YAAY,gBAAgB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC9D;IAEA,MAAM,gBAAgB,CAAC,OAAe;QACpC,MAAM,cAAc;eAAI;SAAgB;QACxC,WAAW,CAAC,MAAM,GAAG;YAAE,GAAG,WAAW,CAAC,MAAM;YAAE;QAAM;QACpD,SAAS,YAAY;IACvB;IAEA,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAE/D,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;kBAE3B,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,iNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;8CAAM,cAAc,cAAc;;;;;;;;;;;;sCAErC,6LAAC,mIAAA,CAAA,kBAAe;sCACb,cAAc,6BAA6B;;;;;;;;;;;;8BAIhD,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;0CAEhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,aAAY;4CACZ,OAAO,CAAC,CAAC,OAAO,KAAK;4CACrB,YAAY,OAAO,KAAK,EAAE;4CACzB,GAAG,SAAS,QAAQ;;;;;;;;;;;kDAIzB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,WAAU;gDACV,MAAM;gDACN,aAAY;gDACX,GAAG,SAAS,cAAc;;;;;;4CAE5B,OAAO,WAAW,kBACjB,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kDAKxE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;0DACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;wDAAyB,WAAU;;0EAClC,6LAAC;gEACC,MAAK;gEACL,OAAO,OAAO,KAAK;gEAClB,GAAG,SAAS,WAAW;gEACxB,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAW,CAAC,oDAAoD,EACnE,oBAAoB,OAAO,KAAK,GAC5B,+BACA,yCACJ;0EACA,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAW,OAAO,IAAI;;;;;;sFACtC,6LAAC;4EAAK,WAAU;sFAAuB,OAAO,KAAK;;;;;;;;;;;;;;;;;;uDAd7C,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0CAwBhC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;;gDACb,eAAe,SAAS;gDAAO;;;;;;;sDAElC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ,eAAe,MAAM;4CAAE;4CAC1C,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;gDAAgB,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKjC,6LAAC,4LAAA,CAAA,kBAAe;0CACb,8BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAO;oCACtC,MAAM;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCAC9B,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,WAAU;4DACT,GAAG,SAAS,WAAW;;8EAExB,6LAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC;wEAAsB,OAAO;kFAC3B;uEADU;;;;;;;;;;;;;;;;;8DAOnB,6LAAC;8DACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAM;wDACN,MAAK;wDACJ,GAAG,SAAS,WAAW;;;;;;;;;;;;;;;;;sDAM9B,6LAAC;sDACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,CAAC,CAAC,OAAO,kBAAkB;gDAClC,YAAY,OAAO,kBAAkB,EAAE;gDACtC,GAAG,SAAS,sBAAsB;oDAAE,eAAe;gDAAK,EAAE;;;;;;;;;;;sDAK/D,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,QAAQ;;;;;;sEAEvE,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAS,SAAS;4DAAQ,MAAK;sEAC1C,cAAA,6LAAC,yMAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGxB,6LAAC;oDAAI,WAAU;8DACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,6LAAC,oIAAA,CAAA,QAAK;4DAAW,SAAQ;4DAAY,WAAU;;8EAC7C,6LAAC,uMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,6LAAC;8EAAM;;;;;;8EACP,6LAAC;oEACC,MAAK;oEACL,SAAS,IAAM,UAAU;oEACzB,WAAU;8EAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;2DART;;;;;;;;;;;;;;;;sDAgBlB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAG3D,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAS,SAAS;4DAAY,MAAK;4DAAK,SAAQ;;8EAC3D,6LAAC,yMAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC,oIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,QAAQ,KAAK;oEACpB,UAAU,CAAC,IAAM,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;8EAEtD,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAS,IAAM,cAAc;oEAC7B,MAAK;oEACL,SAAQ;oEACR,WAAU;8EAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;2DAbX;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAwBtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAAU;;;;;;kDAG3D,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS;wCACT,aAAa,cAAc,gBAAgB;wCAC3C,UAAU,CAAC;kDAEV,cAAc,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GAhSwB;;QAgBlB,iKAAA,CAAA,UAAO;;;KAhBW;AAkSxB,iDAAiD;AACjD,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IAC5D,qBACE,6LAAC;QAAI,WAAW;QAAW,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACnE,cAAA,6LAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAG3E;MANS", "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/tasks/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { PlusIcon, CheckIcon, XIcon, CalendarIcon, FlagIcon, FilterIcon, SearchIcon, LayoutGridIcon, ListIcon, BarChart3Icon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { Input } from '@/components/ui/Input'\nimport { Loading } from '@/components/ui/Loading'\nimport { Modal } from '@/components/ui/Modal'\nimport DragDropTaskBoard from '@/components/tasks/DragDropTaskBoard'\nimport AdvancedTaskForm from '@/components/forms/AdvancedTaskForm'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport toast from 'react-hot-toast'\n\ntype Task = Database['public']['Tables']['tasks']['Row']\n\nexport default function TasksPage() {\n  const [tasks, setTasks] = useState<Task[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filterPriority, setFilterPriority] = useState<'all' | 'low' | 'medium' | 'high'>('all')\n  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'pending'>('all')\n  const [filterCategory, setFilterCategory] = useState<string>('all')\n  const [viewMode, setViewMode] = useState<'list' | 'board' | 'analytics'>('board')\n  const [editingTask, setEditingTask] = useState<Task | null>(null)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchTasks()\n  }, [])\n\n  const fetchTasks = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('tasks')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setTasks(data || [])\n    } catch (error) {\n      console.error('Error fetching tasks:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleTaskSubmit = async (formData: any) => {\n    setIsSubmitting(true)\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      if (editingTask) {\n        // Update existing task\n        const { error } = await supabase\n          .from('tasks')\n          .update({\n            title: formData.title,\n            description: formData.description || null,\n            priority: formData.priority,\n            due_date: formData.due_date || null,\n            category: formData.category || null,\n            estimated_duration: formData.estimated_duration || null,\n          })\n          .eq('id', editingTask.id)\n\n        if (error) throw error\n        toast.success('Task updated successfully!')\n      } else {\n        // Create new task\n        const { error } = await supabase.from('tasks').insert([\n          {\n            title: formData.title,\n            description: formData.description || null,\n            priority: formData.priority,\n            due_date: formData.due_date || null,\n            category: formData.category || null,\n            estimated_duration: formData.estimated_duration || null,\n            completed: false,\n            user_id: user.id,\n          },\n        ])\n\n        if (error) throw error\n        toast.success('Task created successfully!')\n      }\n\n      setShowAddForm(false)\n      setEditingTask(null)\n      fetchTasks()\n    } catch (error) {\n      console.error('Error saving task:', error)\n      toast.error('Failed to save task. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const toggleTask = async (taskId: string, completed: boolean) => {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .update({ completed: !completed })\n        .eq('id', taskId)\n\n      if (error) throw error\n      fetchTasks()\n    } catch (error) {\n      console.error('Error updating task:', error)\n    }\n  }\n\n  const deleteTask = async (taskId: string) => {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .delete()\n        .eq('id', taskId)\n\n      if (error) throw error\n      toast.success('Task deleted successfully!')\n      fetchTasks()\n    } catch (error) {\n      console.error('Error deleting task:', error)\n      toast.error('Failed to delete task. Please try again.')\n    }\n  }\n\n  const handleTasksReorder = async (reorderedTasks: Task[]) => {\n    setTasks(reorderedTasks)\n    // You could implement server-side ordering here if needed\n  }\n\n  const handleEditTask = (task: Task) => {\n    setEditingTask(task)\n    setShowAddForm(true)\n  }\n\n  const handleCancelForm = () => {\n    setShowAddForm(false)\n    setEditingTask(null)\n  }\n\n  const getPriorityVariant = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'destructive'\n      case 'medium':\n        return 'warning'\n      case 'low':\n        return 'success'\n      default:\n        return 'secondary'\n    }\n  }\n\n  const filteredTasks = tasks.filter(task => {\n    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         task.category?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    const matchesPriority = filterPriority === 'all' || task.priority === filterPriority\n    const matchesStatus = filterStatus === 'all' ||\n                         (filterStatus === 'completed' && task.completed) ||\n                         (filterStatus === 'pending' && !task.completed)\n    const matchesCategory = filterCategory === 'all' || task.category === filterCategory\n\n    return matchesSearch && matchesPriority && matchesStatus && matchesCategory\n  })\n\n  const taskStats = {\n    total: tasks.length,\n    completed: tasks.filter(t => t.completed).length,\n    pending: tasks.filter(t => !t.completed).length,\n    overdue: tasks.filter(t => !t.completed && t.due_date && new Date(t.due_date) < new Date()).length,\n    highPriority: tasks.filter(t => !t.completed && t.priority === 'high').length,\n    dueToday: tasks.filter(t => !t.completed && t.due_date &&\n      new Date(t.due_date).toDateString() === new Date().toDateString()).length\n  }\n\n  const categories = Array.from(new Set(tasks.map(t => t.category).filter(Boolean))) as string[]\n\n  if (loading) {\n    return <Loading size=\"lg\" text=\"Loading your tasks...\" className=\"h-64\" />\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n      >\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Task Management</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Organize, prioritize, and track your tasks with advanced features\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          {/* View Mode Toggle */}\n          <div className=\"flex items-center bg-gray-100 rounded-lg p-1\">\n            <Button\n              variant={viewMode === 'board' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('board')}\n              className=\"h-8\"\n            >\n              <LayoutGridIcon className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('list')}\n              className=\"h-8\"\n            >\n              <ListIcon className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'analytics' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('analytics')}\n              className=\"h-8\"\n            >\n              <BarChart3Icon className=\"h-4 w-4\" />\n            </Button>\n          </div>\n\n          <Button onClick={() => setShowAddForm(true)} size=\"lg\">\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Add Task\n          </Button>\n        </div>\n      </motion.div>\n\n      {/* Enhanced Stats Cards */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n        className=\"grid grid-cols-2 lg:grid-cols-6 gap-3 sm:gap-4\"\n      >\n        <Card className=\"hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500\">\n          <CardContent className=\"p-3 sm:p-4\">\n            <div className=\"text-center\">\n              <div className=\"text-xl sm:text-2xl font-bold text-blue-600\">{taskStats.total}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Total Tasks</div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card className=\"hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500\">\n          <CardContent className=\"p-3 sm:p-4\">\n            <div className=\"text-center\">\n              <div className=\"text-xl sm:text-2xl font-bold text-green-600\">{taskStats.completed}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Completed</div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card className=\"hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500\">\n          <CardContent className=\"p-3 sm:p-4\">\n            <div className=\"text-center\">\n              <div className=\"text-xl sm:text-2xl font-bold text-orange-600\">{taskStats.pending}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Pending</div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card className=\"hover:shadow-lg transition-all duration-300 border-l-4 border-l-red-500\">\n          <CardContent className=\"p-3 sm:p-4\">\n            <div className=\"text-center\">\n              <div className=\"text-xl sm:text-2xl font-bold text-red-600\">{taskStats.overdue}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Overdue</div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card className=\"hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500\">\n          <CardContent className=\"p-3 sm:p-4\">\n            <div className=\"text-center\">\n              <div className=\"text-xl sm:text-2xl font-bold text-purple-600\">{taskStats.highPriority}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">High Priority</div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card className=\"hover:shadow-lg transition-all duration-300 border-l-4 border-l-indigo-500\">\n          <CardContent className=\"p-3 sm:p-4\">\n            <div className=\"text-center\">\n              <div className=\"text-xl sm:text-2xl font-bold text-indigo-600\">{taskStats.dueToday}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Due Today</div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Enhanced Filters and Search */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <FilterIcon className=\"h-5 w-5 text-blue-600\" />\n              <span>Search & Filter</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"relative\">\n              <SearchIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"Search tasks by title, description, or category...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10 w-full\"\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3\">\n              <select\n                value={filterPriority}\n                onChange={(e) => setFilterPriority(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n              >\n                <option value=\"all\">All Priorities</option>\n                <option value=\"high\">🔴 High Priority</option>\n                <option value=\"medium\">🟡 Medium Priority</option>\n                <option value=\"low\">🟢 Low Priority</option>\n              </select>\n\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"pending\">⏳ Pending</option>\n                <option value=\"completed\">✅ Completed</option>\n              </select>\n\n              <select\n                value={filterCategory}\n                onChange={(e) => setFilterCategory(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n              >\n                <option value=\"all\">All Categories</option>\n                {categories.map((category) => (\n                  <option key={category} value={category}>\n                    📁 {category}\n                  </option>\n                ))}\n              </select>\n\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSearchTerm('')\n                  setFilterPriority('all')\n                  setFilterStatus('all')\n                  setFilterCategory('all')\n                }}\n                className=\"flex items-center space-x-2\"\n              >\n                <XIcon className=\"h-4 w-4\" />\n                <span>Clear Filters</span>\n              </Button>\n            </div>\n\n            {/* Active Filters Display */}\n            {(searchTerm || filterPriority !== 'all' || filterStatus !== 'all' || filterCategory !== 'all') && (\n              <div className=\"flex flex-wrap gap-2 pt-2 border-t\">\n                <span className=\"text-sm text-gray-600\">Active filters:</span>\n                {searchTerm && (\n                  <Badge variant=\"secondary\">Search: \"{searchTerm}\"</Badge>\n                )}\n                {filterPriority !== 'all' && (\n                  <Badge variant=\"secondary\">Priority: {filterPriority}</Badge>\n                )}\n                {filterStatus !== 'all' && (\n                  <Badge variant=\"secondary\">Status: {filterStatus}</Badge>\n                )}\n                {filterCategory !== 'all' && (\n                  <Badge variant=\"secondary\">Category: {filterCategory}</Badge>\n                )}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Task Views */}\n      <AnimatePresence mode=\"wait\">\n        {viewMode === 'board' && (\n          <motion.div\n            key=\"board\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: 20 }}\n            transition={{ duration: 0.3 }}\n          >\n            <DragDropTaskBoard\n              tasks={filteredTasks}\n              onTasksReorder={handleTasksReorder}\n              onToggleTask={toggleTask}\n              onDeleteTask={deleteTask}\n            />\n          </motion.div>\n        )}\n\n        {viewMode === 'list' && (\n          <motion.div\n            key=\"list\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: 20 }}\n            transition={{ duration: 0.3 }}\n          >\n            {/* Traditional List View */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Task List ({filteredTasks.length})</CardTitle>\n                <CardDescription>\n                  {filteredTasks.length} of {tasks.length} tasks\n                  {searchTerm && ` matching \"${searchTerm}\"`}\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                {filteredTasks.length === 0 ? (\n                  <div className=\"text-center py-12\">\n                    <CheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <h3 className=\"mt-2 text-lg font-medium text-gray-900\">\n                      {tasks.length === 0 ? 'No tasks yet' : 'No matching tasks'}\n                    </h3>\n                    <p className=\"mt-1 text-gray-500\">\n                      {tasks.length === 0\n                        ? 'Get started by creating your first task.'\n                        : 'Try adjusting your search or filters.'}\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-3\">\n                    {filteredTasks.map((task, index) => (\n                      <motion.div\n                        key={task.id}\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ delay: index * 0.05 }}\n                        className={`p-4 rounded-lg border transition-all duration-200 hover:shadow-md ${\n                          task.completed ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-200 hover:border-blue-300'\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex items-start space-x-3 flex-1\">\n                            <button\n                              onClick={() => toggleTask(task.id, task.completed)}\n                              className={`flex-shrink-0 mt-1 h-5 w-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${\n                                task.completed\n                                  ? 'bg-green-600 border-green-600 hover:bg-green-700'\n                                  : 'border-gray-300 hover:border-blue-500 hover:bg-blue-50'\n                              }`}\n                            >\n                              {task.completed && <CheckIcon className=\"h-3 w-3 text-white\" />}\n                            </button>\n                            <div className=\"flex-1 min-w-0\">\n                              <h3 className={`text-sm font-medium transition-all duration-200 ${\n                                task.completed ? 'line-through text-gray-500' : 'text-gray-900'\n                              }`}>\n                                {task.title}\n                              </h3>\n                              {task.description && (\n                                <p className={`text-sm mt-1 ${\n                                  task.completed ? 'text-gray-400' : 'text-gray-600'\n                                }`}>\n                                  {task.description}\n                                </p>\n                              )}\n                              <div className=\"flex items-center mt-2 space-x-2 flex-wrap gap-1\">\n                                <Badge variant={getPriorityVariant(task.priority)} size=\"sm\">\n                                  <FlagIcon className=\"h-3 w-3 mr-1\" />\n                                  {task.priority}\n                                </Badge>\n                                {task.category && (\n                                  <Badge variant=\"outline\" size=\"sm\">\n                                    {task.category}\n                                  </Badge>\n                                )}\n                                {task.due_date && (\n                                  <Badge\n                                    variant={\n                                      !task.completed && new Date(task.due_date) < new Date()\n                                        ? 'destructive'\n                                        : 'secondary'\n                                    }\n                                    size=\"sm\"\n                                  >\n                                    <CalendarIcon className=\"h-3 w-3 mr-1\" />\n                                    {new Date(task.due_date).toLocaleDateString()}\n                                  </Badge>\n                                )}\n                              </div>\n                            </div>\n                          </div>\n                          <div className=\"flex items-center space-x-1\">\n                            <Button\n                              onClick={() => handleEditTask(task)}\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              className=\"text-blue-600 hover:text-blue-700 hover:bg-blue-50\"\n                            >\n                              Edit\n                            </Button>\n                            <Button\n                              onClick={() => deleteTask(task.id)}\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                            >\n                              <XIcon className=\"h-4 w-4\" />\n                            </Button>\n                          </div>\n                        </div>\n                      </motion.div>\n                    ))}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </motion.div>\n        )}\n\n        {viewMode === 'analytics' && (\n          <motion.div\n            key=\"analytics\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: 20 }}\n            transition={{ duration: 0.3 }}\n          >\n            <Card>\n              <CardHeader>\n                <CardTitle>Task Analytics</CardTitle>\n                <CardDescription>Insights and statistics about your task management</CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div className=\"space-y-4\">\n                    <h4 className=\"font-medium text-gray-900\">Completion Rate</h4>\n                    <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                      <div\n                        className=\"bg-blue-600 h-3 rounded-full transition-all duration-500\"\n                        style={{ width: `${tasks.length > 0 ? (taskStats.completed / tasks.length) * 100 : 0}%` }}\n                      ></div>\n                    </div>\n                    <p className=\"text-sm text-gray-600\">\n                      {tasks.length > 0 ? Math.round((taskStats.completed / tasks.length) * 100) : 0}% of tasks completed\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <h4 className=\"font-medium text-gray-900\">Priority Distribution</h4>\n                    <div className=\"space-y-2\">\n                      {['high', 'medium', 'low'].map((priority) => {\n                        const count = tasks.filter(t => t.priority === priority).length\n                        const percentage = tasks.length > 0 ? (count / tasks.length) * 100 : 0\n                        return (\n                          <div key={priority} className=\"flex items-center justify-between\">\n                            <span className=\"text-sm capitalize\">{priority} Priority</span>\n                            <div className=\"flex items-center space-x-2\">\n                              <div className=\"w-20 bg-gray-200 rounded-full h-2\">\n                                <div\n                                  className={`h-2 rounded-full ${\n                                    priority === 'high' ? 'bg-red-500' :\n                                    priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'\n                                  }`}\n                                  style={{ width: `${percentage}%` }}\n                                ></div>\n                              </div>\n                              <span className=\"text-sm text-gray-600\">{count}</span>\n                            </div>\n                          </div>\n                        )\n                      })}\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Advanced Task Form Modal */}\n      <Modal\n        isOpen={showAddForm}\n        onClose={handleCancelForm}\n        size=\"lg\"\n      >\n        <AdvancedTaskForm\n          onSubmit={handleTaskSubmit}\n          onCancel={handleCancelForm}\n          initialData={editingTask || undefined}\n          isLoading={isSubmitting}\n        />\n      </Modal>\n\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAfA;;;;;;;;;;;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IACxF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAClF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,SAAS,QAAQ,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,IAAI,aAAa;gBACf,uBAAuB;gBACvB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW,IAAI;oBACrC,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ,IAAI;oBAC/B,UAAU,SAAS,QAAQ,IAAI;oBAC/B,oBAAoB,SAAS,kBAAkB,IAAI;gBACrD,GACC,EAAE,CAAC,MAAM,YAAY,EAAE;gBAE1B,IAAI,OAAO,MAAM;gBACjB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,kBAAkB;gBAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;oBACpD;wBACE,OAAO,SAAS,KAAK;wBACrB,aAAa,SAAS,WAAW,IAAI;wBACrC,UAAU,SAAS,QAAQ;wBAC3B,UAAU,SAAS,QAAQ,IAAI;wBAC/B,UAAU,SAAS,QAAQ,IAAI;wBAC/B,oBAAoB,SAAS,kBAAkB,IAAI;wBACnD,WAAW;wBACX,SAAS,KAAK,EAAE;oBAClB;iBACD;gBAED,IAAI,OAAO,MAAM;gBACjB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;YAEA,eAAe;YACf,eAAe;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,OAAO,QAAgB;QACxC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;gBAAE,WAAW,CAAC;YAAU,GAC/B,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,SAAS;IACT,0DAA0D;IAC5D;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,KAAK,QAAQ,EAAE,cAAc,SAAS,WAAW,WAAW;QAEjF,MAAM,kBAAkB,mBAAmB,SAAS,KAAK,QAAQ,KAAK;QACtE,MAAM,gBAAgB,iBAAiB,SACjB,iBAAiB,eAAe,KAAK,SAAS,IAC9C,iBAAiB,aAAa,CAAC,KAAK,SAAS;QACnE,MAAM,kBAAkB,mBAAmB,SAAS,KAAK,QAAQ,KAAK;QAEtE,OAAO,iBAAiB,mBAAmB,iBAAiB;IAC9D;IAEA,MAAM,YAAY;QAChB,OAAO,MAAM,MAAM;QACnB,WAAW,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;QAChD,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EAAE,MAAM;QAC/C,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,IAAI,IAAI,KAAK,EAAE,QAAQ,IAAI,IAAI,QAAQ,MAAM;QAClG,cAAc,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,KAAK,QAAQ,MAAM;QAC7E,UAAU,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,IACpD,IAAI,KAAK,EAAE,QAAQ,EAAE,YAAY,OAAO,IAAI,OAAO,YAAY,IAAI,MAAM;IAC7E;IAEA,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;IAExE,IAAI,SAAS;QACX,qBAAO,6LAAC,sIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;YAAwB,WAAU;;;;;;IACnE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,UAAU,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,6LAAC,yMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,cAAc,YAAY;wCAChD,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI7B,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,eAAe;gCAAO,MAAK;;kDAChD,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA+C,UAAU,KAAK;;;;;;kDAC7E,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;kCAIxD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgD,UAAU,SAAS;;;;;;kDAClF,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;kCAIxD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiD,UAAU,OAAO;;;;;;kDACjF,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;kCAIxD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA8C,UAAU,OAAO;;;;;;kDAC9E,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;kCAIxD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiD,UAAU,YAAY;;;;;;kDACtF,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;kCAIxD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiD,UAAU,QAAQ;;;;;;kDAClF,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;sDAGtB,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;sDAG5B,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;gDACnB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAsB,OAAO;;4DAAU;4DAClC;;uDADO;;;;;;;;;;;sDAMjB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;gDACP,cAAc;gDACd,kBAAkB;gDAClB,gBAAgB;gDAChB,kBAAkB;4CACpB;4CACA,WAAU;;8DAEV,6LAAC,mMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;gCAKT,CAAC,cAAc,mBAAmB,SAAS,iBAAiB,SAAS,mBAAmB,KAAK,mBAC5F,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;wCACvC,4BACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAY;gDAAU;gDAAW;;;;;;;wCAEjD,mBAAmB,uBAClB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAY;gDAAW;;;;;;;wCAEvC,iBAAiB,uBAChB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAY;gDAAS;;;;;;;wCAErC,mBAAmB,uBAClB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAY;gDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlD,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;;oBACnB,aAAa,yBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC,mJAAA,CAAA,UAAiB;4BAChB,OAAO;4BACP,gBAAgB;4BAChB,cAAc;4BACd,cAAc;;;;;;uBAVZ;;;;;oBAeP,aAAa,wBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAG5B,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;;gDAAC;gDAAY,cAAc,MAAM;gDAAC;;;;;;;sDAC5C,6LAAC,mIAAA,CAAA,kBAAe;;gDACb,cAAc,MAAM;gDAAC;gDAAK,MAAM,MAAM;gDAAC;gDACvC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;;;;;;;;;;;;;8CAG9C,6LAAC,mIAAA,CAAA,cAAW;8CACT,cAAc,MAAM,KAAK,kBACxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAG,WAAU;0DACX,MAAM,MAAM,KAAK,IAAI,iBAAiB;;;;;;0DAEzC,6LAAC;gDAAE,WAAU;0DACV,MAAM,MAAM,KAAK,IACd,6CACA;;;;;;;;;;;6DAIR,6LAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,QAAQ;gDAAK;gDAClC,WAAW,CAAC,kEAAkE,EAC5E,KAAK,SAAS,GAAG,+BAA+B,kDAChD;0DAEF,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,SAAS,IAAM,WAAW,KAAK,EAAE,EAAE,KAAK,SAAS;oEACjD,WAAW,CAAC,yGAAyG,EACnH,KAAK,SAAS,GACV,qDACA,0DACJ;8EAED,KAAK,SAAS,kBAAI,6LAAC,2MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;8EAE1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAW,CAAC,gDAAgD,EAC9D,KAAK,SAAS,GAAG,+BAA+B,iBAChD;sFACC,KAAK,KAAK;;;;;;wEAEZ,KAAK,WAAW,kBACf,6LAAC;4EAAE,WAAW,CAAC,aAAa,EAC1B,KAAK,SAAS,GAAG,kBAAkB,iBACnC;sFACC,KAAK,WAAW;;;;;;sFAGrB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAS,mBAAmB,KAAK,QAAQ;oFAAG,MAAK;;sGACtD,6LAAC,yMAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;wFACnB,KAAK,QAAQ;;;;;;;gFAEf,KAAK,QAAQ,kBACZ,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,MAAK;8FAC3B,KAAK,QAAQ;;;;;;gFAGjB,KAAK,QAAQ,kBACZ,6LAAC,oIAAA,CAAA,QAAK;oFACJ,SACE,CAAC,KAAK,SAAS,IAAI,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,SAC7C,gBACA;oFAEN,MAAK;;sGAEL,6LAAC,iNAAA,CAAA,eAAY;4FAAC,WAAU;;;;;;wFACvB,IAAI,KAAK,KAAK,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;sEAMrD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAS,IAAM,eAAe;oEAC9B,SAAQ;oEACR,MAAK;oEACL,WAAU;8EACX;;;;;;8EAGD,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAS,IAAM,WAAW,KAAK,EAAE;oEACjC,SAAQ;oEACR,MAAK;oEACL,WAAU;8EAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CA1ElB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;uBAhCpB;;;;;oBAuHP,aAAa,6BACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,MAAM,MAAM,GAAG,IAAI,AAAC,UAAU,SAAS,GAAG,MAAM,MAAM,GAAI,MAAM,EAAE,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAG5F,6LAAC;wDAAE,WAAU;;4DACV,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,UAAU,SAAS,GAAG,MAAM,MAAM,GAAI,OAAO;4DAAE;;;;;;;;;;;;;0DAInF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,6LAAC;wDAAI,WAAU;kEACZ;4DAAC;4DAAQ;4DAAU;yDAAM,CAAC,GAAG,CAAC,CAAC;4DAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;4DAC/D,MAAM,aAAa,MAAM,MAAM,GAAG,IAAI,AAAC,QAAQ,MAAM,MAAM,GAAI,MAAM;4DACrE,qBACE,6LAAC;gEAAmB,WAAU;;kFAC5B,6LAAC;wEAAK,WAAU;;4EAAsB;4EAAS;;;;;;;kFAC/C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC;oFACC,WAAW,CAAC,iBAAiB,EAC3B,aAAa,SAAS,eACtB,aAAa,WAAW,kBAAkB,gBAC1C;oFACF,OAAO;wFAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oFAAC;;;;;;;;;;;0FAGrC,6LAAC;gFAAK,WAAU;0FAAyB;;;;;;;;;;;;;+DAZnC;;;;;wDAgBd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAjDN;;;;;;;;;;;0BA4DV,6LAAC,oIAAA,CAAA,QAAK;gBACJ,QAAQ;gBACR,SAAS;gBACT,MAAK;0BAEL,cAAA,6LAAC,kJAAA,CAAA,UAAgB;oBACf,UAAU;oBACV,UAAU;oBACV,aAAa,eAAe;oBAC5B,WAAW;;;;;;;;;;;;;;;;;AAMrB;GAjlBwB;KAAA", "debugId": null}}]}