import { cn } from '@/lib/utils'

describe('cn utility function', () => {
  it('merges class names correctly', () => {
    const result = cn('px-4', 'py-2', 'bg-blue-500')
    expect(result).toBe('px-4 py-2 bg-blue-500')
  })

  it('handles conditional classes', () => {
    const isActive = true
    const result = cn('base-class', isActive && 'active-class')
    expect(result).toBe('base-class active-class')
  })

  it('handles conflicting Tailwind classes', () => {
    const result = cn('px-4 px-6', 'py-2')
    // Should keep the last px class (px-6)
    expect(result).toContain('px-6')
    expect(result).not.toContain('px-4')
  })

  it('filters out falsy values', () => {
    const result = cn('base', null, undefined, false, 'valid')
    expect(result).toBe('base valid')
  })

  it('handles arrays of classes', () => {
    const result = cn(['class1', 'class2'], 'class3')
    expect(result).toBe('class1 class2 class3')
  })
})
