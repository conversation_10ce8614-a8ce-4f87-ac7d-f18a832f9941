{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lzxq+LF28sVcjYZHZyFkzVYZUvwisl0bINLEPMHycW8=", "__NEXT_PREVIEW_MODE_ID": "cbb38ec0e1fb3f002dace6e579bf4e55", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9f0e008292af5cba03c2c18cc32389051f9dd6466cf6ce5409b943e94ff4b9a9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "eea8451daac747b3448f10b4797e6c9543316ab03f877962327b4a4850c4556e"}}}, "instrumentation": null, "functions": {}}