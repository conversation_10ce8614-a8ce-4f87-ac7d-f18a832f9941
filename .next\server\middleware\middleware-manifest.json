{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lzxq+LF28sVcjYZHZyFkzVYZUvwisl0bINLEPMHycW8=", "__NEXT_PREVIEW_MODE_ID": "065574402128ae6754b5b0c42a3f988b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "624ab05e04064d78dc4f08b6d4b422ed8b61df5a85eda0f961ecd57fbdb8a437", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "41151ec6fa8a4166a40e4ef13850d1d5fd7ef97787849ba6e77ec7c8ad79763e"}}}, "instrumentation": null, "functions": {}}