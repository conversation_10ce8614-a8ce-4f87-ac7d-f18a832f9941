{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport { useRouter } from 'next/navigation'\nimport {\n  HomeIcon,\n  CheckSquareIcon,\n  DollarSignIcon,\n  ShoppingCartIcon,\n  MessageCircleIcon,\n  ChefHatIcon,\n  LogOutIcon,\n  MenuIcon,\n  XIcon,\n  UserIcon,\n  SettingsIcon,\n  BellIcon,\n  SparklesIcon,\n} from 'lucide-react'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, color: 'text-blue-600' },\n  { name: 'Tasks', href: '/tasks', icon: CheckSquareIcon, color: 'text-green-600' },\n  { name: 'Budget', href: '/budget', icon: DollarSignIcon, color: 'text-emerald-600' },\n  { name: 'Shopping Lists', href: '/shopping', icon: ShoppingCartIcon, color: 'text-purple-600' },\n  { name: 'AI Chat', href: '/chat', icon: MessageCircleIcon, color: 'text-indigo-600' },\n  { name: 'Recipes', href: '/recipes', icon: ChefHatIcon, color: 'text-orange-600' },\n]\n\nexport default function Sidebar() {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const [user, setUser] = useState<any>(null)\n  const pathname = usePathname()\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n    }\n    getUser()\n  }, [])\n\n  const handleLogout = async () => {\n    await supabase.auth.signOut()\n    router.push('/login')\n    router.refresh()\n  }\n\n  const getUserInitials = (email: string) => {\n    return email.split('@')[0].slice(0, 2).toUpperCase()\n  }\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white hover:bg-gray-600 transition-colors\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XIcon className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n\n          {/* Header with logo and user */}\n          <div className=\"flex-shrink-0 px-4 py-6 bg-gradient-to-r from-blue-600 to-indigo-600\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  <SparklesIcon className=\"h-8 w-8 text-white\" />\n                </div>\n                <h1 className=\"text-xl font-bold text-white\">LifeManager</h1>\n              </div>\n            </div>\n            {user && (\n              <div className=\"mt-4 flex items-center space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-white\">\n                      {getUserInitials(user.email)}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"min-w-0 flex-1\">\n                  <p className=\"text-sm font-medium text-white truncate\">\n                    {user.email}\n                  </p>\n                  <p className=\"text-xs text-blue-100\">\n                    Welcome back!\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <nav className=\"px-3 space-y-1\" role=\"navigation\" aria-label=\"Main navigation\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setSidebarOpen(false)}\n                    className={`group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${\n                      isActive\n                        ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-600'\n                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon\n                      className={`mr-4 h-6 w-6 transition-colors ${\n                        isActive ? 'text-blue-600' : `${item.color} group-hover:${item.color}`\n                      }`}\n                      aria-hidden=\"true\"\n                    />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 border-t border-gray-200 p-4\">\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <LogOutIcon className=\"mr-3 h-5 w-5\" />\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 z-30\">\n        <div className=\"flex-1 flex flex-col min-h-0 bg-white shadow-lg border-r border-gray-200\">\n          {/* Header with logo and user */}\n          <div className=\"flex-shrink-0 px-4 py-6 bg-gradient-to-r from-blue-600 to-indigo-600\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <SparklesIcon className=\"h-8 w-8 text-white\" />\n              </div>\n              <h1 className=\"text-xl font-bold text-white\">LifeManager</h1>\n            </div>\n            {user && (\n              <div className=\"mt-4 flex items-center space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-white\">\n                      {getUserInitials(user.email)}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"min-w-0 flex-1\">\n                  <p className=\"text-sm font-medium text-white truncate\">\n                    {user.email}\n                  </p>\n                  <p className=\"text-xs text-blue-100\">\n                    Welcome back!\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <nav className=\"flex-1 px-3 space-y-1\" role=\"navigation\" aria-label=\"Main navigation\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${\n                      isActive\n                        ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-600 shadow-sm'\n                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'\n                    }`}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon\n                      className={`mr-3 h-5 w-5 transition-colors ${\n                        isActive ? 'text-blue-600' : `${item.color} group-hover:${item.color}`\n                      }`}\n                      aria-hidden=\"true\"\n                    />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 border-t border-gray-200 p-4\">\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <LogOutIcon className=\"mr-3 h-5 w-5\" />\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu button */}\n      <div className=\"md:hidden\">\n        <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-4 py-3 shadow-sm sticky top-0 z-40\">\n          <div className=\"flex items-center space-x-2\">\n            <SparklesIcon className=\"h-6 w-6 text-blue-600\" />\n            <h1 className=\"text-lg font-semibold text-gray-900 truncate\">LifeManager</h1>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            {user && (\n              <div className=\"flex items-center space-x-2 mr-2\">\n                <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n                  <span className=\"text-xs font-medium text-blue-700\">\n                    {getUserInitials(user.email)}\n                  </span>\n                </div>\n              </div>\n            )}\n            <button\n              type=\"button\"\n              className=\"touch-target inline-flex items-center justify-center p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors\"\n            >\n              <BellIcon className=\"h-5 w-5\" />\n            </button>\n            <button\n              type=\"button\"\n              className=\"touch-target inline-flex items-center justify-center p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <MenuIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAuBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,uMAAA,CAAA,WAAQ;QAAE,OAAO;IAAgB;IAChF;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,+NAAA,CAAA,kBAAe;QAAE,OAAO;IAAiB;IAChF;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,sNAAA,CAAA,iBAAc;QAAE,OAAO;IAAmB;IACnF;QAAE,MAAM;QAAkB,MAAM;QAAa,MAAM,0NAAA,CAAA,mBAAgB;QAAE,OAAO;IAAkB;IAC9F;QAAE,MAAM;QAAW,MAAM;QAAS,MAAM,4NAAA,CAAA,oBAAiB;QAAE,OAAO;IAAkB;IACpF;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,gNAAA,CAAA,cAAW;QAAE,OAAO;IAAkB;CAClF;AAEc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,QAAQ;QACV;QACA;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,OAAO,IAAI,CAAC;QACZ,OAAO,OAAO;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;IACpD;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA6D,SAAS,IAAM,eAAe;;;;;;kCAC1G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC,gMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC;oDAAG,WAAU;8DAA+B;;;;;;;;;;;;;;;;;oCAGhD,sBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAiB,MAAK;oCAAa,cAAW;8CAC1D,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI;wCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe;4CAC9B,WAAW,CAAC,uKAAuK,EACjL,WACI,wDACA,sDACJ;4CACF,gBAAc,WAAW,SAAS;;8DAElC,8OAAC,KAAK,IAAI;oDACR,WAAW,CAAC,+BAA+B,EACzC,WAAW,kBAAkB,GAAG,KAAK,KAAK,CAAC,aAAa,EAAE,KAAK,KAAK,EAAE,EACtE;oDACF,eAAY;;;;;;gDAEb,KAAK,IAAI;;2CAhBL,KAAK,IAAI;;;;;oCAmBpB;;;;;;;;;;;0CAIJ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;;;;;;;gCAE9C,sBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAa,cAAW;0CACjE,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,qKAAqK,EAC/K,WACI,kEACA,sEACJ;wCACF,gBAAc,WAAW,SAAS;;0DAElC,8OAAC,KAAK,IAAI;gDACR,WAAW,CAAC,+BAA+B,EACzC,WAAW,kBAAkB,GAAG,KAAK,KAAK,CAAC,aAAa,EAAE,KAAK,KAAK,EAAE,EACtE;gDACF,eAAY;;;;;;4CAEb,KAAK,IAAI;;uCAfL,KAAK,IAAI;;;;;gCAkBpB;;;;;;;;;;;sCAIJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;;;;;;;sCAE/D,8OAAC;4BAAI,WAAU;;gCACZ,sBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;8CAKnC,8OAAC;oCACC,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/AuthenticatedLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport Sidebar from './Sidebar'\n\ninterface AuthenticatedLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {\n  const [loading, setLoading] = useState(true)\n  const [user, setUser] = useState<any>(null)\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const { data: { user }, error } = await supabase.auth.getUser()\n        \n        if (error || !user) {\n          router.push('/login')\n          return\n        }\n        \n        setUser(user)\n      } catch (error) {\n        console.error('Auth check error:', error)\n        router.push('/login')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    checkAuth()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_OUT' || !session) {\n          router.push('/login')\n        } else if (event === 'SIGNED_IN' && session) {\n          setUser(session.user)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [router, supabase.auth])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-6 animate-fade-in\">\n          <div className=\"relative\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-blue-200\"></div>\n            <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent absolute top-0 left-0\"></div>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-gray-700 text-lg font-medium\">Loading your workspace...</p>\n            <p className=\"text-gray-500 text-sm mt-1\">Please wait while we prepare everything</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect to login\n  }\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gradient-to-br from-gray-50 to-blue-50\">\n      {/* Skip Navigation Links */}\n      <a\n        href=\"#main-content\"\n        className=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n      >\n        Skip to main content\n      </a>\n\n      <Sidebar />\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        <main\n          id=\"main-content\"\n          className=\"flex-1 relative overflow-y-auto focus:outline-none md:ml-64\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"py-6 animate-fade-in\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWe,SAAS,oBAAoB,EAAE,QAAQ,EAA4B;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;gBAE7D,IAAI,SAAS,CAAC,MAAM;oBAClB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,IAAI,UAAU,gBAAgB,CAAC,SAAS;gBACtC,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,eAAe,SAAS;gBAC3C,QAAQ,QAAQ,IAAI;YACtB;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAQ,SAAS,IAAI;KAAC;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,yBAAyB;;IACvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;0BAID,8OAAC,uIAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,IAAG;oBACH,WAAU;oBACV,MAAK;oBACL,cAAW;8BAEX,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}