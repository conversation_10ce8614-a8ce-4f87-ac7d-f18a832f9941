import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import * as cheerio from 'cheerio'

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json()

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch the webpage
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })
    
    if (!response.ok) {
      throw new Error('Failed to fetch recipe page')
    }

    const html = await response.text()
    const $ = cheerio.load(html)

    // Try to extract recipe data using JSON-LD structured data
    let recipeData = null
    $('script[type="application/ld+json"]').each((_, element) => {
      try {
        const jsonData = JSON.parse($(element).html() || '')
        if (jsonData['@type'] === 'Recipe' || (Array.isArray(jsonData) && jsonData.some(item => item['@type'] === 'Recipe'))) {
          recipeData = Array.isArray(jsonData) ? jsonData.find(item => item['@type'] === 'Recipe') : jsonData
          return false // Break the loop
        }
      } catch (e) {
        // Continue to next script tag
      }
    })

    // If no structured data found, try to extract from common HTML patterns
    if (!recipeData) {
      recipeData = extractRecipeFromHTML($)
    }

    if (!recipeData) {
      return NextResponse.json({ error: 'Could not extract recipe data from URL' }, { status: 400 })
    }

    // Save recipe to database
    const { data, error } = await supabase
      .from('recipes')
      .insert([
        {
          user_id: user.id,
          title: recipeData.name || 'Imported Recipe',
          description: recipeData.description || '',
          ingredients: recipeData.recipeIngredient || [],
          instructions: recipeData.recipeInstructions || [],
          prep_time: parseTime(recipeData.prepTime),
          cook_time: parseTime(recipeData.cookTime),
          servings: recipeData.recipeYield ? parseInt(recipeData.recipeYield) : null,
          difficulty: 'medium',
          cuisine: recipeData.recipeCuisine || null,
          image_url: recipeData.image || null,
          source_url: url,
        },
      ])
      .select()
      .single()

    if (error) throw error

    return NextResponse.json({ recipe: data })
  } catch (error) {
    console.error('Error importing recipe:', error)
    return NextResponse.json(
      { error: 'Failed to import recipe' },
      { status: 500 }
    )
  }
}

function extractRecipeFromHTML($: cheerio.CheerioAPI) {
  // Try common selectors for recipe data
  const title = $('h1').first().text().trim() || 
                $('.recipe-title').first().text().trim() ||
                $('[class*="title"]').first().text().trim()

  const description = $('.recipe-description').first().text().trim() ||
                     $('[class*="description"]').first().text().trim() ||
                     $('meta[name="description"]').attr('content')

  // Extract ingredients
  const ingredients: string[] = []
  $('.recipe-ingredient, .ingredient, [class*="ingredient"]').each((_, element) => {
    const text = $(element).text().trim()
    if (text) ingredients.push(text)
  })

  // Extract instructions
  const instructions: string[] = []
  $('.recipe-instruction, .instruction, [class*="instruction"], .recipe-step, .step').each((_, element) => {
    const text = $(element).text().trim()
    if (text) instructions.push(text)
  })

  // Extract image
  const image = $('img[class*="recipe"], img[class*="hero"]').first().attr('src') ||
                $('.recipe-image img').first().attr('src')

  if (!title && ingredients.length === 0) {
    return null
  }

  return {
    name: title,
    description: description,
    recipeIngredient: ingredients,
    recipeInstructions: instructions.map(instruction => ({ text: instruction })),
    image: image
  }
}

function parseTime(timeString: string | undefined): number | null {
  if (!timeString) return null
  
  // Parse ISO 8601 duration (PT15M = 15 minutes)
  const isoMatch = timeString.match(/PT(?:(\d+)H)?(?:(\d+)M)?/)
  if (isoMatch) {
    const hours = parseInt(isoMatch[1] || '0')
    const minutes = parseInt(isoMatch[2] || '0')
    return hours * 60 + minutes
  }
  
  // Parse simple number (assume minutes)
  const numberMatch = timeString.match(/(\d+)/)
  if (numberMatch) {
    return parseInt(numberMatch[1])
  }
  
  return null
}
