import { render, screen } from '@testing-library/react'

// Simple component test for basic functionality
const MockHomePage = () => {
  return (
    <div>
      <h1>Manage Your Life Effortlessly</h1>
      <div>
        <h2>Task Management</h2>
        <h2>Budget Tracking</h2>
        <h2>Shopping Lists</h2>
        <h2>AI Assistant</h2>
        <h2>Recipe Manager</h2>
      </div>
      <div>
        <a href="/signup">Get Started</a>
        <a href="/login">Sign In</a>
      </div>
      <footer>
        <h3>LifeManager</h3>
      </footer>
    </div>
  )
}

describe('Home Page Components', () => {
  it('renders the main heading', () => {
    render(<MockHomePage />)

    expect(screen.getByText(/Manage Your Life/i)).toBeInTheDocument()
    expect(screen.getByText(/Effortlessly/i)).toBeInTheDocument()
  })

  it('displays all feature sections', () => {
    render(<MockHomePage />)

    expect(screen.getByText('Task Management')).toBeInTheDocument()
    expect(screen.getByText('Budget Tracking')).toBeInTheDocument()
    expect(screen.getByText('Shopping Lists')).toBeInTheDocument()
    expect(screen.getByText('AI Assistant')).toBeInTheDocument()
    expect(screen.getByText('Recipe Manager')).toBeInTheDocument()
  })

  it('has sign up and sign in links', () => {
    render(<MockHomePage />)

    expect(screen.getByText('Get Started')).toBeInTheDocument()
    expect(screen.getByText('Sign In')).toBeInTheDocument()
  })

  it('displays the company name', () => {
    render(<MockHomePage />)

    expect(screen.getByText('LifeManager')).toBeInTheDocument()
  })
})
