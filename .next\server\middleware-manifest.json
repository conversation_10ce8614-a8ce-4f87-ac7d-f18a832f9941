{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lzxq+LF28sVcjYZHZyFkzVYZUvwisl0bINLEPMHycW8=", "__NEXT_PREVIEW_MODE_ID": "aa1ad34eef443cd0f389876647bf1731", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e19f2a2270b69b2d122a5c873ccc6e9a48807b2f5b38cebabf745c5ebb4b2d25", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a755827dccd619e0abbb4629c42d36f54b0baeb69935825b5a8d55af7b632ac7"}}}, "sortedMiddleware": ["/"], "functions": {}}