{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { SendIcon, MessageCircleIcon, PlusIcon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\n\ntype ChatConversation = Database['public']['Tables']['chat_conversations']['Row']\ntype ChatMessage = Database['public']['Tables']['chat_messages']['Row']\n\nexport default function ChatPage() {\n  const [conversations, setConversations] = useState<ChatConversation[]>([])\n  const [currentConversation, setCurrentConversation] = useState<string | null>(null)\n  const [messages, setMessages] = useState<ChatMessage[]>([])\n  const [newMessage, setNewMessage] = useState('')\n  const [loading, setLoading] = useState(true)\n  const [sending, setSending] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchConversations()\n  }, [])\n\n  useEffect(() => {\n    if (currentConversation) {\n      fetchMessages(currentConversation)\n    }\n  }, [currentConversation])\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  const fetchConversations = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('chat_conversations')\n        .select('*')\n        .order('updated_at', { ascending: false })\n\n      if (error) throw error\n      setConversations(data || [])\n      \n      if (data && data.length > 0 && !currentConversation) {\n        setCurrentConversation(data[0].id)\n      }\n    } catch (error) {\n      console.error('Error fetching conversations:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchMessages = async (conversationId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('chat_messages')\n        .select('*')\n        .eq('conversation_id', conversationId)\n        .order('created_at', { ascending: true })\n\n      if (error) throw error\n      setMessages(data || [])\n    } catch (error) {\n      console.error('Error fetching messages:', error)\n    }\n  }\n\n  const createNewConversation = async () => {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { data, error } = await supabase\n        .from('chat_conversations')\n        .insert([\n          {\n            user_id: user.id,\n            title: 'New Conversation',\n          },\n        ])\n        .select()\n        .single()\n\n      if (error) throw error\n      \n      setConversations([data, ...conversations])\n      setCurrentConversation(data.id)\n      setMessages([])\n    } catch (error) {\n      console.error('Error creating conversation:', error)\n    }\n  }\n\n  const sendMessage = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!newMessage.trim() || !currentConversation || sending) return\n\n    setSending(true)\n    const messageText = newMessage.trim()\n    setNewMessage('')\n\n    try {\n      // Add user message to UI immediately\n      const userMessage: ChatMessage = {\n        id: 'temp-' + Date.now(),\n        conversation_id: currentConversation,\n        role: 'user',\n        content: messageText,\n        created_at: new Date().toISOString(),\n      }\n      setMessages(prev => [...prev, userMessage])\n\n      // Send to API\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageText,\n          conversationId: currentConversation,\n        }),\n      })\n\n      if (!response.ok) throw new Error('Failed to send message')\n\n      const { response: aiResponse } = await response.json()\n\n      // Add AI response to UI\n      const aiMessage: ChatMessage = {\n        id: 'temp-ai-' + Date.now(),\n        conversation_id: currentConversation,\n        role: 'assistant',\n        content: aiResponse,\n        created_at: new Date().toISOString(),\n      }\n      setMessages(prev => [...prev, aiMessage])\n\n      // Refresh messages from database to get real IDs\n      setTimeout(() => fetchMessages(currentConversation), 500)\n    } catch (error) {\n      console.error('Error sending message:', error)\n    } finally {\n      setSending(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"h-[calc(100vh-8rem)] flex\">\n      {/* Conversations Sidebar */}\n      <div className=\"w-1/4 bg-white border-r border-gray-200 flex flex-col\">\n        <div className=\"p-4 border-b border-gray-200\">\n          <button\n            onClick={createNewConversation}\n            className=\"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            New Chat\n          </button>\n        </div>\n        <div className=\"flex-1 overflow-y-auto\">\n          {conversations.length === 0 ? (\n            <div className=\"p-4 text-center text-gray-500\">\n              <MessageCircleIcon className=\"mx-auto h-8 w-8 mb-2\" />\n              <p className=\"text-sm\">No conversations yet</p>\n            </div>\n          ) : (\n            <div className=\"space-y-1 p-2\">\n              {conversations.map((conversation) => (\n                <button\n                  key={conversation.id}\n                  onClick={() => setCurrentConversation(conversation.id)}\n                  className={`w-full text-left p-3 rounded-lg text-sm ${\n                    currentConversation === conversation.id\n                      ? 'bg-blue-100 text-blue-900'\n                      : 'hover:bg-gray-100'\n                  }`}\n                >\n                  <div className=\"font-medium truncate\">{conversation.title}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    {new Date(conversation.updated_at).toLocaleDateString()}\n                  </div>\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Chat Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {currentConversation ? (\n          <>\n            {/* Messages */}\n            <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n              {messages.length === 0 ? (\n                <div className=\"text-center text-gray-500 mt-8\">\n                  <MessageCircleIcon className=\"mx-auto h-12 w-12 mb-4\" />\n                  <h3 className=\"text-lg font-medium\">Start a conversation</h3>\n                  <p className=\"text-sm\">Ask me anything about managing your life!</p>\n                </div>\n              ) : (\n                messages.map((message) => (\n                  <div\n                    key={message.id}\n                    className={`flex ${\n                      message.role === 'user' ? 'justify-end' : 'justify-start'\n                    }`}\n                  >\n                    <div\n                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                        message.role === 'user'\n                          ? 'bg-blue-600 text-white'\n                          : 'bg-gray-200 text-gray-900'\n                      }`}\n                    >\n                      <p className=\"text-sm\">{message.content}</p>\n                      <p className={`text-xs mt-1 ${\n                        message.role === 'user' ? 'text-blue-200' : 'text-gray-500'\n                      }`}>\n                        {new Date(message.created_at).toLocaleTimeString()}\n                      </p>\n                    </div>\n                  </div>\n                ))\n              )}\n              <div ref={messagesEndRef} />\n            </div>\n\n            {/* Message Input */}\n            <div className=\"border-t border-gray-200 p-4\">\n              <form onSubmit={sendMessage} className=\"flex space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newMessage}\n                  onChange={(e) => setNewMessage(e.target.value)}\n                  placeholder=\"Type your message...\"\n                  className=\"flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  disabled={sending}\n                />\n                <button\n                  type=\"submit\"\n                  disabled={!newMessage.trim() || sending}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <SendIcon className=\"h-4 w-4\" />\n                </button>\n              </form>\n            </div>\n          </>\n        ) : (\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div className=\"text-center text-gray-500\">\n              <MessageCircleIcon className=\"mx-auto h-12 w-12 mb-4\" />\n              <h3 className=\"text-lg font-medium\">Select a conversation</h3>\n              <p className=\"text-sm\">Choose a conversation from the sidebar or start a new one</p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB;YACvB,cAAc;QAChB;IACF,GAAG;QAAC;KAAoB;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,iBAAiB,QAAQ,EAAE;YAE3B,IAAI,QAAQ,KAAK,MAAM,GAAG,KAAK,CAAC,qBAAqB;gBACnD,uBAAuB,IAAI,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,mBAAmB,gBACtB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAK;YAEzC,IAAI,OAAO,MAAM;YACjB,YAAY,QAAQ,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;gBACN;oBACE,SAAS,KAAK,EAAE;oBAChB,OAAO;gBACT;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,iBAAiB;gBAAC;mBAAS;aAAc;YACzC,uBAAuB,KAAK,EAAE;YAC9B,YAAY,EAAE;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,uBAAuB,SAAS;QAE3D,WAAW;QACX,MAAM,cAAc,WAAW,IAAI;QACnC,cAAc;QAEd,IAAI;YACF,qCAAqC;YACrC,MAAM,cAA2B;gBAC/B,IAAI,UAAU,KAAK,GAAG;gBACtB,iBAAiB;gBACjB,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAE1C,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,EAAE,UAAU,UAAU,EAAE,GAAG,MAAM,SAAS,IAAI;YAEpD,wBAAwB;YACxB,MAAM,YAAyB;gBAC7B,IAAI,aAAa,KAAK,GAAG;gBACzB,iBAAiB;gBACjB,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YAExC,iDAAiD;YACjD,WAAW,IAAM,cAAc,sBAAsB;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4NAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;8CAC7B,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;iDAGzB,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,SAAS,IAAM,uBAAuB,aAAa,EAAE;oCACrD,WAAW,CAAC,wCAAwC,EAClD,wBAAwB,aAAa,EAAE,GACnC,8BACA,qBACJ;;sDAEF,8OAAC;4CAAI,WAAU;sDAAwB,aAAa,KAAK;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDACZ,IAAI,KAAK,aAAa,UAAU,EAAE,kBAAkB;;;;;;;mCAVlD,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;0BAoBhC,8OAAC;gBAAI,WAAU;0BACZ,oCACC;;sCAEE,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4NAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;2CAGzB,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC;wCAEC,WAAW,CAAC,KAAK,EACf,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAC1C;kDAEF,cAAA,8OAAC;4CACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;;8DAEF,8OAAC;oDAAE,WAAU;8DAAW,QAAQ,OAAO;;;;;;8DACvC,8OAAC;oDAAE,WAAW,CAAC,aAAa,EAC1B,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;8DACC,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;uCAhB/C,QAAQ,EAAE;;;;;8CAsBrB,8OAAC;oCAAI,KAAK;;;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAa,WAAU;;kDACrC,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,UAAU,CAAC,WAAW,IAAI,MAAM;wCAChC,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;iDAM5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4NAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;0CAC7B,8OAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,8OAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC", "debugId": null}}]}